// Original file: proto/channelz.proto

import type { Duration as _google_protobuf_Duration, Duration__Output as _google_protobuf_Duration__Output } from '../../../google/protobuf/Duration';

/**
 * For use with SocketOption's additional field.  This is primarily used for
 * SO_RCVTIMEO and SO_SNDTIMEO
 */
export interface SocketOptionTimeout {
  'duration'?: (_google_protobuf_Duration | null);
}

/**
 * For use with SocketOption's additional field.  This is primarily used for
 * SO_RCVTIMEO and SO_SNDTIMEO
 */
export interface SocketOptionTimeout__Output {
  'duration': (_google_protobuf_Duration__Output | null);
}
