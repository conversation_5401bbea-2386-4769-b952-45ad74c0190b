# Changelog

[npm history][1]

[1]: https://www.npmjs.com/package/nodejs-precise-date?activeTab=versions

## [3.0.1](https://github.com/googleapis/nodejs-precise-date/compare/v3.0.0...v3.0.1) (2022-08-23)


### Bug Fixes

* remove pip install statements ([#1546](https://github.com/googleapis/nodejs-precise-date/issues/1546)) ([#219](https://github.com/googleapis/nodejs-precise-date/issues/219)) ([8a81e94](https://github.com/googleapis/nodejs-precise-date/commit/8a81e94c0430ff801978bedf8827f2b978f352fb))

## [3.0.0](https://github.com/googleapis/nodejs-precise-date/compare/v2.0.4...v3.0.0) (2022-06-07)


### ⚠ BREAKING CHANGES

* update library to use Node 12 (#215)

### Build System

* update library to use Node 12 ([#215](https://github.com/googleapis/nodejs-precise-date/issues/215)) ([209a3d1](https://github.com/googleapis/nodejs-precise-date/commit/209a3d1a8a0bf4841c695c6fffe5ef663857161f))

### [2.0.4](https://www.github.com/googleapis/nodejs-precise-date/compare/v2.0.3...v2.0.4) (2021-09-09)


### Bug Fixes

* **build:** switch primary branch to main ([#186](https://www.github.com/googleapis/nodejs-precise-date/issues/186)) ([aaa4044](https://www.github.com/googleapis/nodejs-precise-date/commit/aaa4044fdf81aaeee8b62e00f71abd9b9d1a0827))

### [2.0.3](https://www.github.com/googleapis/nodejs-precise-date/compare/v2.0.2...v2.0.3) (2020-07-24)


### Bug Fixes

* move gitattributes files to node templates ([#130](https://www.github.com/googleapis/nodejs-precise-date/issues/130)) ([d4de5dd](https://www.github.com/googleapis/nodejs-precise-date/commit/d4de5dd73a4aec17c4c8c1e8a95335e1d3e656e5))

### [2.0.2](https://www.github.com/googleapis/nodejs-precise-date/compare/v2.0.1...v2.0.2) (2020-07-06)


### Bug Fixes

* update node issue template ([#117](https://www.github.com/googleapis/nodejs-precise-date/issues/117)) ([ae4d6a1](https://www.github.com/googleapis/nodejs-precise-date/commit/ae4d6a1d07e5327d25eda58e709f3d547ffca607))

### [2.0.1](https://www.github.com/googleapis/nodejs-precise-date/compare/v2.0.0...v2.0.1) (2020-05-08)


### Bug Fixes

* apache license URL ([#468](https://www.github.com/googleapis/nodejs-precise-date/issues/468)) ([#103](https://www.github.com/googleapis/nodejs-precise-date/issues/103)) ([efdc8c6](https://www.github.com/googleapis/nodejs-precise-date/commit/efdc8c6a1e5f8b35da4c5e6ff03fa1f2be6c790f))

## [2.0.0](https://www.github.com/googleapis/nodejs-precise-date/compare/v1.0.3...v2.0.0) (2020-03-26)


### ⚠ BREAKING CHANGES

* **dep:** upgrade typescript/gts (#97)
* **dep:** deprecate Node 8 to 10 (#92)

### Miscellaneous Chores

* **dep:** deprecate Node 8 to 10 ([#92](https://www.github.com/googleapis/nodejs-precise-date/issues/92)) ([75ca209](https://www.github.com/googleapis/nodejs-precise-date/commit/75ca209b49abcba8efcbc401b270ac1346874647))
* **dep:** upgrade typescript/gts ([#97](https://www.github.com/googleapis/nodejs-precise-date/issues/97)) ([59ca2de](https://www.github.com/googleapis/nodejs-precise-date/commit/59ca2de8f6da249808dddebdba3961e59140d06d))

### [1.0.3](https://www.github.com/googleapis/nodejs-precise-date/compare/v1.0.2...v1.0.3) (2019-12-05)


### Bug Fixes

* **deps:** pin TypeScript below 3.7.0 ([c4aef9c](https://www.github.com/googleapis/nodejs-precise-date/commit/c4aef9c42cfab91c4701891d12a7b7118e3ba76c))

### [1.0.2](https://www.github.com/googleapis/nodejs-precise-date/compare/v1.0.1...v1.0.2) (2019-11-15)


### Bug Fixes

* **docs:** add jsdoc-region-tag plugin ([#60](https://www.github.com/googleapis/nodejs-precise-date/issues/60)) ([a975b1c](https://www.github.com/googleapis/nodejs-precise-date/commit/a975b1c39b9ad283b48eb8e2cb13d9eb1cb053ba))

### [1.0.1](https://www.github.com/googleapis/nodejs-precise-date/compare/v1.0.0...v1.0.1) (2019-06-27)


### Bug Fixes

* **docs:** make anchors work in jsdoc ([#43](https://www.github.com/googleapis/nodejs-precise-date/issues/43)) ([5828db8](https://www.github.com/googleapis/nodejs-precise-date/commit/5828db8))

## [1.0.0](https://www.github.com/googleapis/nodejs-precise-date/compare/v0.1.0...v1.0.0) (2019-05-02)


### Build System

* upgrade engines field to >=8.10.0 ([#25](https://www.github.com/googleapis/nodejs-precise-date/issues/25)) ([10b9cc1](https://www.github.com/googleapis/nodejs-precise-date/commit/10b9cc1))


### BREAKING CHANGES

* upgrade engines field to >=8.10.0 (#25)

## v0.1.0

02-19-2019 12:27 PST

### Hello, world!
- feat: initial code commit (#1)

### Documentation
- docs: update links in contrib guide (#4)

### Internal / Testing Changes
- chore: add missing boilerplate files (#3)
- chore: add dummy docs-test script and fix tests for Node 11 (#6)
