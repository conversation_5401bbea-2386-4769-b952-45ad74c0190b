{"name": "@google-cloud/promisify", "version": "2.0.4", "description": "A simple utility for promisifying functions and classes.", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "googleapis/nodejs-promisify", "scripts": {"test": "c8 mocha build/test", "lint": "gts check", "compile": "tsc -p .", "fix": "gts fix", "prepare": "npm run compile", "pretest": "npm run compile", "docs": "compodoc src/", "presystem-test": "npm run compile", "samples-test": "cd samples/ && npm link ../ && npm test && cd ../", "system-test": "mocha build/system-test", "docs-test": "linkinator docs", "predocs-test": "npm run docs", "prelint": "cd samples; npm link ../; npm install", "clean": "gts clean", "precompile": "gts clean"}, "keywords": [], "files": ["build/src", "!build/src/**/*.map"], "author": "Google Inc.", "license": "Apache-2.0", "devDependencies": {"@compodoc/compodoc": "^1.1.9", "@types/mocha": "^8.0.0", "@types/node": "^14.0.0", "@types/sinon": "^10.0.0", "c8": "^7.0.0", "chai": "^4.2.0", "codecov": "^3.0.4", "gts": "^2.0.0", "hard-rejection": "^2.1.0", "linkinator": "^2.0.0", "mocha": "^8.0.0", "sinon": "^11.0.0", "typescript": "^3.8.3"}, "engines": {"node": ">=10"}}