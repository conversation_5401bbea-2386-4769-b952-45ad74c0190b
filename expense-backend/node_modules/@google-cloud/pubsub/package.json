{"name": "@google-cloud/pubsub", "description": "Cloud Pub/Sub Client Library for Node.js", "version": "3.7.5", "license": "Apache-2.0", "author": "Google Inc.", "engines": {"node": ">=12.0.0"}, "repository": "googleapis/nodejs-pubsub", "main": "./build/src/index.js", "types": "./build/src/index.d.ts", "files": ["build/protos", "build/src"], "keywords": ["google apis client", "google api client", "google apis", "google api", "google", "google cloud platform", "google cloud", "cloud", "google pubsub", "pubsub"], "scripts": {"presystem-test": "npm run compile", "system-test": "mocha build/system-test --timeout 600000", "samples-test": "cd samples/ && npm link ../ && npm install && npm test && cd ../", "test": "c8 mocha build/test --recursive", "lint": "gts check", "predocs": "npm run compile", "docs": "jsdoc -c .jsdoc.js", "fix": "gts fix", "clean": "gts clean", "compile": "tsc -p . && cp -r protos build/", "compile-protos": "compileProtos src", "prepare": "npm run compile-protos && npm run compile", "pretest": "npm run compile", "docs-test": "linkinator docs", "predocs-test": "npm run docs", "benchwrapper": "node bin/benchwrapper.js", "prelint": "cd samples; npm link ../; npm install", "precompile": "gts clean"}, "dependencies": {"@google-cloud/paginator": "^4.0.0", "@google-cloud/precise-date": "^3.0.0", "@google-cloud/projectify": "^3.0.0", "@google-cloud/promisify": "^2.0.0", "@opentelemetry/api": "^1.6.0", "@opentelemetry/semantic-conventions": "~1.3.0", "@types/duplexify": "^3.6.0", "@types/long": "^4.0.0", "arrify": "^2.0.0", "extend": "^3.0.2", "google-auth-library": "^8.0.2", "google-gax": "^3.6.1", "heap-js": "^2.2.0", "is-stream-ended": "^0.1.4", "lodash.snakecase": "^4.1.1", "p-defer": "^3.0.0"}, "devDependencies": {"@grpc/proto-loader": "^0.7.0", "@opentelemetry/tracing": "^0.24.0", "@types/execa": "^0.9.0", "@types/extend": "^3.0.0", "@types/lodash.snakecase": "^4.1.6", "@types/mocha": "^9.0.0", "@types/mv": "^2.1.0", "@types/ncp": "^2.0.1", "@types/node": "^18.0.0", "@types/proxyquire": "^1.3.28", "@types/sinon": "^10.0.0", "@types/tmp": "^0.2.0", "@types/uuid": "^9.0.0", "c8": "^8.0.0", "codecov": "^3.0.0", "execa": "^5.0.0", "gts": "^3.1.0", "jsdoc": "^4.0.0", "jsdoc-fresh": "^2.0.0", "jsdoc-region-tag": "^2.0.0", "linkinator": "^5.0.0", "mocha": "^9.2.2", "mv": "^2.1.1", "ncp": "^2.0.0", "null-loader": "^4.0.0", "protobufjs": "^7.0.0", "proxyquire": "^2.0.0", "sinon": "^15.0.0", "tmp": "^0.2.0", "ts-loader": "^9.0.0", "typescript": "^4.6.4", "uuid": "^9.0.0", "webpack": "^5.0.0", "webpack-cli": "^5.0.0", "yargs": "^16.0.0"}}