{"name": "@opentelemetry/semantic-conventions", "version": "1.3.1", "description": "OpenTelemetry semantic conventions", "main": "build/src/index.js", "module": "build/esm/index.js", "esnext": "build/esnext/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js", "scripts": {"prepublishOnly": "npm run compile", "compile": "tsc --build tsconfig.all.json", "clean": "tsc --build --clean tsconfig.all.json", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "version": "node ../../scripts/version-update.js", "watch": "tsc --build --watch tsconfig.all.json", "precompile": "lerna run version --scope $(npm pkg get name) --include-dependencies", "prewatch": "npm run precompile", "peer-api-check": "node ../../scripts/peer-api-check.js"}, "keywords": ["opentelemetry", "nodejs", "tracing", "attributes", "semantic conventions"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=8.12.0"}, "files": ["build/esm/**/*.js", "build/esm/**/*.js.map", "build/esm/**/*.d.ts", "build/esnext/**/*.js", "build/esnext/**/*.js.map", "build/esnext/**/*.d.ts", "build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts", "doc", "LICENSE", "README.md"], "publishConfig": {"access": "public"}, "devDependencies": {"@types/mocha": "8.2.3", "@types/node": "14.17.33", "@types/sinon": "10.0.6", "codecov": "3.8.3", "mocha": "7.2.0", "nock": "13.0.11", "nyc": "15.1.0", "rimraf": "3.0.2", "sinon": "12.0.1", "ts-mocha": "9.0.2", "typescript": "4.4.4"}, "gitHead": "51afd54bd63e46d5d530266761144c7be2f6b3a7"}