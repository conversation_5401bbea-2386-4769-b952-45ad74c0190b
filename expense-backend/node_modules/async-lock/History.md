1.3.2
==================
* Handle errors from user-supplied callback better (Thank you @simeonborko)
* Ensure the array parameter does not get mutated for batch acquire (Thank you @taschmidt)

1.3.1
==================
* Show queue name in errors (thank you @luke-stead-sonocent)

1.3.0
==================
* Add maxOccupationTime option (Thank you @abozaralizadeh and @ThePiz)

1.2.8
==================
* Fix #37 process not set when acquiring lock (Thank you @Philipp91)

1.2.7
==================
DO NOT USE, erroneous publish

1.2.6
==================
* Fix `maxPending = Infinity` no longer allowed  (thank you @coderaiser)

1.2.5
==================
* Allow `maxPending = 0`  (thank you @bmrpatel)
* Upgrade dependencies

1.2.4
==================
* Be robust to lock names that are also Object keys - simpler solution, extra tests.

1.2.3
==================
* Be robust to lock names that are also Object keys.
* Upgrade dependencies

1.2.2
==================
* Fix grunt-env accidentally having become a dependency (#25)

1.2.1
==================
* Remove empty postinstall script (#24)
* Fixed some vulnerable dev dependencies

1.2.0
==================
* ES5 compatibility (#21)

1.1.4
==================
* Fix for #17, update dependencies

1.1.3
==================
* Fix for #14

1.1.1
==================
* Fix result Promise not resolving when locking empty key array

1.1.0 / 2017-10-17
==================
* Add option to add waiters to the front of the queue

1.1.0 / 2017-10-17
==================
* Add option to add waiters to the front of the queue

1.0.0 / 2017-06-29
==================
* Remove dependency on Q by using the global Promise variable by default. Thank you @erikvold (https://github.com/erikvold) for the PR

0.3.10 / 2017-06-27
==================
* Remove dependencies on Q-specific nodify(), try(), and defer() methods so that you can inject e.g. standard ES6 promises using `new AsyncLock({ Promise: Promise })`

0.3.9 / 2016-11-30
==================
* Referred to MIT license in package.json
* Update NPM module dependencies, remove no-longer-maintained blanket code coverage
* Change author to rogierschouten
* Fix invalid JSDoc comments (thanks @JonathanPicques)
* Bugfix: TypeError when locking multiple keys at once

0.3.7-0.3.8
==================
Unknown, previous author did not commit this to Github.

0.3.6 / 2015-09-07
==================
* Performance improvement

0.3.5 / 2015-06-15
==================
* Performance improvement

0.3.4 / 2015-06-09
==================
* Bug fix

0.3.3 / 2015-05-19
==================
* Bug fix

0.3.2 / 2015-05-08
==================
* Set default timeout to never

0.3.1 / 2015-04-15
==================
* Use your own promise

0.3.0 / 2015-03-06
==================
* Domain reentrant

0.2.0 / 2015-02-21
==================
* Support promise mode
* Pending task limit

0.1.0 / 2015-01-13
==================
* Initial version
