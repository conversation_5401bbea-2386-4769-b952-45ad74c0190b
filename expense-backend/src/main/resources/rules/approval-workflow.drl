package rules;

import com.example.expense_backend.entity.Expense;
import com.example.expense_backend.entity.User;
import com.example.expense_backend.entity.Department;
import com.example.expense_backend.entity.UserRole;
import com.example.expense_backend.service.rules.RuleResult;
import com.example.expense_backend.service.rules.ApprovalRequirement;
import java.math.BigDecimal;

/**
 * APPROVAL WORKFLOW RULES
 * 
 * These rules demonstrate enterprise workflow patterns:
 * - Multi-level approval chains
 * - Role-based decision making
 * - Dynamic approval routing
 * - Escalation policies
 */

// Global variables
global RuleResult ruleResult;

rule "Auto-Approve Small IT Expenses"
    salience 100
    when
        $expense : Expense( amount < 100, category == "Software" )
        $user : User() from $expense.getUser()
        $dept : Department() from $user.getDepartment()
        eval( $dept != null && "IT".equals($dept.getCode()) )
    then
        ruleResult.addApprovalDecision(
            "AUTO_APPROVED",
            "Auto-approved: Small IT software expense under $100",
            $expense.getId(),
            null
        );
        System.out.println("Rule fired: Auto-approved IT expense " + $expense.getId());
end

rule "Department Head Approval Required"
    salience 90
    when
        $expense : Expense( amount >= 100, amount <= 1000 )
        $user : User() from $expense.getUser()
        $dept : Department() from $user.getDepartment()
        eval( $dept != null )
    then
        ruleResult.addApprovalRequirement(
            new ApprovalRequirement(
                "DEPARTMENT_HEAD",
                "Department head approval required for expenses $100-$1000",
                $dept.getDepartmentHead(),
                1
            )
        );
        System.out.println("Rule fired: Department head approval required for expense " + $expense.getId());
end

rule "Finance Manager Approval Required"
    salience 80
    when
        $expense : Expense( amount > 1000, amount <= 5000 )
    then
        ruleResult.addApprovalRequirement(
            new ApprovalRequirement(
                "FINANCE_MANAGER",
                "Finance manager approval required for expenses $1000-$5000",
                null,
                2
            )
        );
        System.out.println("Rule fired: Finance manager approval required for expense " + $expense.getId());
end

rule "Executive Approval Required"
    salience 70
    when
        $expense : Expense( amount > 5000 )
    then
        ruleResult.addApprovalRequirement(
            new ApprovalRequirement(
                "EXECUTIVE",
                "Executive approval required for expenses over $5000",
                null,
                3
            )
        );
        System.out.println("Rule fired: Executive approval required for expense " + $expense.getId());
end
