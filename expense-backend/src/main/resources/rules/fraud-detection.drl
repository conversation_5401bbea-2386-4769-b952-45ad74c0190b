package rules;

import com.example.expense_backend.entity.Expense;
import com.example.expense_backend.entity.User;
import com.example.expense_backend.service.rules.RuleResult;
import java.math.BigDecimal;

/**
 * FRAUD DETECTION RULES
 * 
 * These rules demonstrate enterprise security patterns:
 * - Anomaly detection
 * - Pattern recognition
 * - Risk scoring
 * - Automated flagging
 */

// Global variables
global RuleResult ruleResult;

rule "Round Number Suspicion"
    salience 90
    when
        $expense : Expense( amount != null )
        eval( $expense.getAmount().remainder(new BigDecimal("100")).equals(BigDecimal.ZERO) &&
              $expense.getAmount().compareTo(new BigDecimal("500")) > 0 )
    then
        ruleResult.addFraudAlert(
            "ROUND_NUMBER_PATTERN",
            "Suspicious round number amount over $500",
            $expense.getId(),
            "MEDIUM"
        );
        System.out.println("Rule fired: Round number pattern for expense " + $expense.getId());
end
