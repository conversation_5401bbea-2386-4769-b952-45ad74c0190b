package rules;

import com.example.expense_backend.entity.Expense;
import com.example.expense_backend.entity.User;
import com.example.expense_backend.entity.Department;
import com.example.expense_backend.entity.ExpenseStatus;
import com.example.expense_backend.service.rules.RuleResult;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * EXPENSE VALIDATION RULES
 * 
 * These rules demonstrate enterprise business logic patterns:
 * - Input validation with business context
 * - Policy-based constraints
 * - Dynamic rule evaluation
 * - Audit trail generation
 */

// Global variables for rule results
global RuleResult ruleResult;

rule "Require Business Purpose for Large Expenses"
    salience 100
    when
        $expense : Expense( amount > 100 )
        eval( $expense.getBusinessPurpose() == null || $expense.getBusinessPurpose().trim().length() < 10 )
    then
        ruleResult.addValidationError(
            "BUSINESS_PURPOSE_REQUIRED",
            "Business purpose must be at least 10 characters for expenses over $100",
            $expense.getId()
        );
        System.out.println("Rule fired: Business purpose required for expense " + $expense.getId());
end

rule "Block Alcohol Purchases"
    salience 90
    when
        $expense : Expense( description != null )
        eval( $expense.getDescription().toLowerCase().contains("alcohol") ||
              $expense.getDescription().toLowerCase().contains("wine") ||
              $expense.getDescription().toLowerCase().contains("beer") ||
              $expense.getDescription().toLowerCase().contains("liquor") )
    then
        ruleResult.addValidationError(
            "ALCOHOL_NOT_PERMITTED",
            "Alcohol purchases are not permitted under company policy",
            $expense.getId()
        );
        System.out.println("Rule fired: Alcohol purchase blocked for expense " + $expense.getId());
end

rule "Require Receipt for Meals Over $25"
    salience 80
    when
        $expense : Expense( category == "Meals", amount > 25 )
    then
        ruleResult.addValidationWarning(
            "RECEIPT_RECOMMENDED",
            "Receipt strongly recommended for meal expenses over $25",
            $expense.getId()
        );
        System.out.println("Rule fired: Receipt recommended for meal expense " + $expense.getId());
end

rule "Validate Future Date Expenses"
    salience 70
    when
        $expense : Expense( date != null )
        eval( $expense.getDate().isAfter(LocalDate.now()) )
    then
        ruleResult.addValidationError(
            "FUTURE_DATE_NOT_ALLOWED",
            "Expense date cannot be in the future",
            $expense.getId()
        );
        System.out.println("Rule fired: Future date blocked for expense " + $expense.getId());
end

rule "Validate Expense Amount"
    salience 50
    when
        $expense : Expense()
        eval( $expense.getAmount() == null || $expense.getAmount().compareTo(new BigDecimal("0")) <= 0 )
    then
        ruleResult.addValidationError(
            "INVALID_AMOUNT",
            "Expense amount must be greater than zero",
            $expense.getId()
        );
        System.out.println("Rule fired: Invalid amount for expense " + $expense.getId());
end

rule "Large Expense Alert"
    salience 40
    when
        $expense : Expense( amount > 5000 )
    then
        ruleResult.addValidationWarning(
            "LARGE_EXPENSE_ALERT",
            "Large expense detected - additional scrutiny may be required",
            $expense.getId()
        );
        System.out.println("Rule fired: Large expense alert for expense " + $expense.getId());
end
