package rules;

import com.example.expense_backend.entity.Expense;
import com.example.expense_backend.entity.User;
import com.example.expense_backend.entity.Department;
import com.example.expense_backend.service.rules.RuleResult;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.DayOfWeek;

/**
 * POLICY ENFORCEMENT RULES
 * 
 * These rules demonstrate enterprise policy patterns:
 * - Department-specific policies
 * - Time-based restrictions
 * - Compliance enforcement
 * - Business rule hierarchies
 */

// Global variables
global RuleResult ruleResult;

rule "Meal Per Diem Policy"
    salience 70
    when
        $expense : Expense( category == "Meals", amount > 75 )
    then
        ruleResult.addPolicyCheck(
            "MEAL_PER_DIEM_POLICY",
            "Meal expenses over $75 exceed standard per diem rates",
            $expense.getId(),
            "EXCEEDS_PER_DIEM"
        );
        System.out.println("Rule fired: Meal per diem policy for expense " + $expense.getId());
end

rule "Training Budget Policy"
    salience 60
    when
        $expense : Expense( category == "Training", amount > 2000 )
    then
        ruleResult.addPolicyCheck(
            "TRAINING_BUDGET_POLICY",
            "Training expenses over $2000 require HR and budget approval",
            $expense.getId(),
            "REQUIRES_HR_APPROVAL"
        );
        System.out.println("Rule fired: Training budget policy for expense " + $expense.getId());
end

rule "Equipment Purchase Policy"
    salience 50
    when
        $expense : Expense( category == "Equipment", amount > 500 )
    then
        ruleResult.addPolicyCheck(
            "EQUIPMENT_POLICY",
            "Equipment purchases over $500 require asset management registration",
            $expense.getId(),
            "REQUIRES_ASSET_REGISTRATION"
        );
        System.out.println("Rule fired: Equipment policy for expense " + $expense.getId());
end
