<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.expense_backend.gui.LoginController">
   <center>
      <VBox alignment="CENTER" spacing="20.0" maxWidth="400.0">
         <children>
            <!-- Logo/Title -->
            <VBox alignment="CENTER" spacing="10.0">
               <children>
                  <Label text="💼" style="-fx-font-size: 48px;" />
                  <Label text="Expense Management System" style="-fx-font-size: 24px; -fx-font-weight: bold;" />
                  <Label text="Enterprise Edition" style="-fx-font-size: 14px; -fx-text-fill: #666;" />
               </children>
            </VBox>
            
            <Separator />
            
            <!-- Login Form -->
            <VBox spacing="15.0">
               <children>
                  <Label text="Login to Your Account" style="-fx-font-size: 18px; -fx-font-weight: bold;" />
                  
                  <VBox spacing="5.0">
                     <children>
                        <Label text="Username:" />
                        <TextField fx:id="usernameField" promptText="Enter your username" prefWidth="300.0" />
                     </children>
                  </VBox>
                  
                  <VBox spacing="5.0">
                     <children>
                        <Label text="Password:" />
                        <PasswordField fx:id="passwordField" promptText="Enter your password" prefWidth="300.0" />
                     </children>
                  </VBox>
                  
                  <Button fx:id="loginButton" text="Login" prefWidth="300.0" prefHeight="35.0" 
                          style="-fx-background-color: #2196F3; -fx-text-fill: white; -fx-font-size: 14px; -fx-font-weight: bold;" />
                  
                  <Label fx:id="statusLabel" text="" wrapText="true" prefWidth="300.0" />
               </children>
            </VBox>
            
            <Separator />
            
            <!-- Demo Users -->
            <VBox spacing="10.0">
               <children>
                  <Label text="Demo Users (Quick Login)" style="-fx-font-size: 14px; -fx-font-weight: bold;" />
                  <ComboBox fx:id="demoUserComboBox" prefWidth="300.0" />
                  
                  <VBox spacing="5.0" style="-fx-background-color: #f5f5f5; -fx-padding: 10;">
                     <children>
                        <Label text="Available Roles:" style="-fx-font-weight: bold; -fx-font-size: 12px;" />
                        <Label text="• Admin - Full system access" style="-fx-font-size: 11px;" />
                        <Label text="• Manager - Team oversight and approvals" style="-fx-font-size: 11px;" />
                        <Label text="• Finance Admin - Financial controls" style="-fx-font-size: 11px;" />
                        <Label text="• Employee - Submit and manage expenses" style="-fx-font-size: 11px;" />
                     </children>
                  </VBox>
               </children>
            </VBox>
         </children>
         <padding>
            <Insets bottom="20.0" left="20.0" right="20.0" top="20.0" />
         </padding>
      </VBox>
   </center>
   
   <bottom>
      <HBox alignment="CENTER" spacing="20.0">
         <children>
            <Label text="Demo Corporation - Expense Management System" style="-fx-font-size: 12px; -fx-text-fill: #888;" />
         </children>
         <padding>
            <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
         </padding>
      </HBox>
   </bottom>
</BorderPane>
