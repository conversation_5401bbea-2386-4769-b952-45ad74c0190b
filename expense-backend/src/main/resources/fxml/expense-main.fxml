<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.expense_backend.gui.ExpenseMainController">
   <top>
      <VBox spacing="10.0">
         <children>
            <!-- Header with User Info -->
            <HBox spacing="20.0" alignment="CENTER_LEFT">
               <children>
                  <VBox>
                     <children>
                        <Label style="-fx-font-size: 24px; -fx-font-weight: bold;" text="💼 Expense Management System" />
                        <Label fx:id="organizationLabel" style="-fx-font-size: 14px; -fx-text-fill: #666;" text="Organization" />
                     </children>
                  </VBox>
                  <Region HBox.hgrow="ALWAYS" />
                  <VBox alignment="CENTER_RIGHT">
                     <children>
                        <Label fx:id="userInfoLabel" style="-fx-font-size: 14px; -fx-font-weight: bold;" text="User Info" />
                        <Label text="Logged in" style="-fx-font-size: 12px; -fx-text-fill: #666;" />
                     </children>
                  </VBox>
               </children>
            </HBox>

            <Separator />

            <!-- Search and Filter Section -->
            <HBox spacing="10.0" alignment="CENTER_LEFT">
               <children>
                  <Label text="Search:" />
                  <TextField fx:id="searchField" promptText="Search by description..." prefWidth="150.0" />

                  <Label text="Category:" />
                  <ComboBox fx:id="filterCategoryComboBox" prefWidth="120.0" />

                  <Label text="Status:" />
                  <ComboBox fx:id="filterStatusComboBox" prefWidth="120.0" />

                  <Label text="From:" />
                  <DatePicker fx:id="fromDatePicker" prefWidth="110.0" />

                  <Label text="To:" />
                  <DatePicker fx:id="toDatePicker" prefWidth="110.0" />

                  <Button fx:id="searchButton" text="Search" />
                  <Button fx:id="clearFiltersButton" text="Clear" />
               </children>
            </HBox>

            <Separator />
         </children>
         <padding>
            <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
         </padding>
      </VBox>
   </top>
   
   <center>
      <SplitPane dividerPositions="0.7" orientation="HORIZONTAL">
         <items>
            <!-- Expense Table -->
            <VBox spacing="10.0">
               <children>
                  <Label style="-fx-font-size: 16px; -fx-font-weight: bold;" text="Expenses" />
                  
                  <TableView fx:id="expenseTable" VBox.vgrow="ALWAYS">
                     <columns>
                        <TableColumn fx:id="expenseNumberColumn" prefWidth="120.0" text="Expense #" />
                        <TableColumn fx:id="descriptionColumn" prefWidth="180.0" text="Description" />
                        <TableColumn fx:id="amountColumn" prefWidth="100.0" text="Amount" />
                        <TableColumn fx:id="dateColumn" prefWidth="100.0" text="Date" />
                        <TableColumn fx:id="categoryColumn" prefWidth="120.0" text="Category" />
                        <TableColumn fx:id="statusColumn" prefWidth="100.0" text="Status" />
                        <TableColumn fx:id="businessPurposeColumn" prefWidth="150.0" text="Business Purpose" />
                        <TableColumn fx:id="submittedByColumn" prefWidth="120.0" text="Submitted By" />
                     </columns>
                  </TableView>
                  
                  <!-- Summary Section -->
                  <HBox spacing="20.0" alignment="CENTER_LEFT">
                     <children>
                        <Label fx:id="totalLabel" style="-fx-font-weight: bold;" text="Total: $0.00" />
                        <Label fx:id="countLabel" text="Count: 0" />
                        <Label fx:id="averageLabel" text="Average: $0.00" />
                     </children>
                  </HBox>
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="5.0" top="10.0" />
               </padding>
            </VBox>
            
            <!-- Add/Edit Form -->
            <VBox spacing="15.0">
               <children>
                  <Label style="-fx-font-size: 16px; -fx-font-weight: bold;" text="Expense Details" />

                  <ScrollPane fitToWidth="true" prefHeight="400.0">
                     <content>
                        <GridPane hgap="10.0" vgap="10.0">
                           <columnConstraints>
                              <ColumnConstraints hgrow="NEVER" minWidth="100.0" />
                              <ColumnConstraints hgrow="ALWAYS" />
                           </columnConstraints>
                           <rowConstraints>
                              <RowConstraints />
                              <RowConstraints />
                              <RowConstraints />
                              <RowConstraints />
                              <RowConstraints />
                              <RowConstraints />
                              <RowConstraints />
                              <RowConstraints />
                              <RowConstraints />
                              <RowConstraints />
                           </rowConstraints>
                           <children>
                              <Label text="Description:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                              <TextField fx:id="descriptionField" promptText="Enter description..." GridPane.columnIndex="1" GridPane.rowIndex="0" />

                              <Label text="Amount:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                              <TextField fx:id="amountField" promptText="0.00" GridPane.columnIndex="1" GridPane.rowIndex="1" />

                              <Label text="Date:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                              <DatePicker fx:id="datePicker" GridPane.columnIndex="1" GridPane.rowIndex="2" />

                              <Label text="Category:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                              <ComboBox fx:id="categoryComboBox" editable="true" promptText="Select or enter category..." GridPane.columnIndex="1" GridPane.rowIndex="3" />

                              <Label text="Business Purpose:" GridPane.columnIndex="0" GridPane.rowIndex="4" />
                              <TextField fx:id="businessPurposeField" promptText="Business justification..." GridPane.columnIndex="1" GridPane.rowIndex="4" />

                              <Label text="Project Code:" GridPane.columnIndex="0" GridPane.rowIndex="5" />
                              <TextField fx:id="projectCodeField" promptText="Project code (optional)" GridPane.columnIndex="1" GridPane.rowIndex="5" />

                              <Label text="Cost Center:" GridPane.columnIndex="0" GridPane.rowIndex="6" />
                              <TextField fx:id="costCenterField" promptText="Cost center (optional)" GridPane.columnIndex="1" GridPane.rowIndex="6" />

                              <Label text="Billable:" GridPane.columnIndex="0" GridPane.rowIndex="7" />
                              <HBox spacing="10.0" GridPane.columnIndex="1" GridPane.rowIndex="7">
                                 <children>
                                    <CheckBox fx:id="billableCheckBox" text="Billable to client" />
                                    <TextField fx:id="clientNameField" promptText="Client name (if billable)" prefWidth="200.0" />
                                 </children>
                              </HBox>

                              <Label text="Notes:" GridPane.columnIndex="0" GridPane.rowIndex="8" />
                              <TextArea fx:id="notesArea" prefRowCount="3" promptText="Additional notes..." GridPane.columnIndex="1" GridPane.rowIndex="8" />
                           </children>
                           <padding>
                              <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
                           </padding>
                        </GridPane>
                     </content>
                  </ScrollPane>
                  
                  <!-- Action Buttons -->
                  <VBox spacing="10.0">
                     <children>
                        <!-- Basic Actions -->
                        <HBox spacing="10.0" alignment="CENTER">
                           <children>
                              <Button fx:id="addButton" prefWidth="80.0" text="Add" style="-fx-background-color: #4CAF50; -fx-text-fill: white;" />
                              <Button fx:id="updateButton" disable="true" prefWidth="80.0" text="Update" style="-fx-background-color: #2196F3; -fx-text-fill: white;" />
                              <Button fx:id="deleteButton" disable="true" prefWidth="80.0" text="Delete" style="-fx-background-color: #f44336; -fx-text-fill: white;" />
                              <Button fx:id="clearButton" prefWidth="80.0" text="Clear" />
                           </children>
                        </HBox>

                        <!-- Workflow Actions -->
                        <HBox spacing="10.0" alignment="CENTER">
                           <children>
                              <Button fx:id="submitButton" disable="true" prefWidth="100.0" text="Submit" style="-fx-background-color: #FF9800; -fx-text-fill: white;" />
                              <Button fx:id="approveButton" disable="true" prefWidth="100.0" text="Approve" style="-fx-background-color: #4CAF50; -fx-text-fill: white;" />
                              <Button fx:id="rejectButton" disable="true" prefWidth="100.0" text="Reject" style="-fx-background-color: #f44336; -fx-text-fill: white;" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
               </children>
               <padding>
                  <Insets bottom="10.0" left="5.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
         </items>
      </SplitPane>
   </center>
</BorderPane>
