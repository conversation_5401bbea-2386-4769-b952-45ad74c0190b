<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.expense_backend.gui.ExpenseMainController">
   <top>
      <VBox spacing="10.0">
         <children>
            <!-- Title -->
            <Label style="-fx-font-size: 24px; -fx-font-weight: bold;" text="Expense Tracker" />
            
            <!-- Search and Filter Section -->
            <HBox spacing="10.0" alignment="CENTER_LEFT">
               <children>
                  <Label text="Search:" />
                  <TextField fx:id="searchField" promptText="Search by description..." prefWidth="200.0" />
                  
                  <Label text="Category:" />
                  <ComboBox fx:id="filterCategoryComboBox" prefWidth="150.0" />
                  
                  <Label text="From:" />
                  <DatePicker fx:id="fromDatePicker" prefWidth="120.0" />
                  
                  <Label text="To:" />
                  <DatePicker fx:id="toDatePicker" prefWidth="120.0" />
                  
                  <Button fx:id="searchButton" text="Search" />
                  <Button fx:id="clearFiltersButton" text="Clear Filters" />
               </children>
            </HBox>
            
            <Separator />
         </children>
         <padding>
            <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
         </padding>
      </VBox>
   </top>
   
   <center>
      <SplitPane dividerPositions="0.7" orientation="HORIZONTAL">
         <items>
            <!-- Expense Table -->
            <VBox spacing="10.0">
               <children>
                  <Label style="-fx-font-size: 16px; -fx-font-weight: bold;" text="Expenses" />
                  
                  <TableView fx:id="expenseTable" VBox.vgrow="ALWAYS">
                     <columns>
                        <TableColumn fx:id="idColumn" prefWidth="50.0" text="ID" />
                        <TableColumn fx:id="descriptionColumn" prefWidth="200.0" text="Description" />
                        <TableColumn fx:id="amountColumn" prefWidth="100.0" text="Amount" />
                        <TableColumn fx:id="dateColumn" prefWidth="100.0" text="Date" />
                        <TableColumn fx:id="categoryColumn" prefWidth="120.0" text="Category" />
                        <TableColumn fx:id="notesColumn" prefWidth="150.0" text="Notes" />
                     </columns>
                  </TableView>
                  
                  <!-- Summary Section -->
                  <HBox spacing="20.0" alignment="CENTER_LEFT">
                     <children>
                        <Label fx:id="totalLabel" style="-fx-font-weight: bold;" text="Total: $0.00" />
                        <Label fx:id="countLabel" text="Count: 0" />
                        <Label fx:id="averageLabel" text="Average: $0.00" />
                     </children>
                  </HBox>
               </children>
               <padding>
                  <Insets bottom="10.0" left="10.0" right="5.0" top="10.0" />
               </padding>
            </VBox>
            
            <!-- Add/Edit Form -->
            <VBox spacing="15.0">
               <children>
                  <Label style="-fx-font-size: 16px; -fx-font-weight: bold;" text="Add/Edit Expense" />
                  
                  <GridPane hgap="10.0" vgap="10.0">
                     <columnConstraints>
                        <ColumnConstraints hgrow="NEVER" minWidth="80.0" />
                        <ColumnConstraints hgrow="ALWAYS" />
                     </columnConstraints>
                     <rowConstraints>
                        <RowConstraints />
                        <RowConstraints />
                        <RowConstraints />
                        <RowConstraints />
                        <RowConstraints />
                     </rowConstraints>
                     <children>
                        <Label text="Description:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <TextField fx:id="descriptionField" promptText="Enter description..." GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        
                        <Label text="Amount:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <TextField fx:id="amountField" promptText="0.00" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        
                        <Label text="Date:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                        <DatePicker fx:id="datePicker" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                        
                        <Label text="Category:" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                        <ComboBox fx:id="categoryComboBox" editable="true" promptText="Select or enter category..." GridPane.columnIndex="1" GridPane.rowIndex="3" />
                        
                        <Label text="Notes:" GridPane.columnIndex="0" GridPane.rowIndex="4" />
                        <TextArea fx:id="notesArea" prefRowCount="3" promptText="Optional notes..." GridPane.columnIndex="1" GridPane.rowIndex="4" />
                     </children>
                  </GridPane>
                  
                  <!-- Action Buttons -->
                  <HBox spacing="10.0" alignment="CENTER">
                     <children>
                        <Button fx:id="addButton" prefWidth="80.0" text="Add" />
                        <Button fx:id="updateButton" disable="true" prefWidth="80.0" text="Update" />
                        <Button fx:id="deleteButton" disable="true" prefWidth="80.0" text="Delete" />
                        <Button fx:id="clearButton" prefWidth="80.0" text="Clear" />
                     </children>
                  </HBox>
               </children>
               <padding>
                  <Insets bottom="10.0" left="5.0" right="10.0" top="10.0" />
               </padding>
            </VBox>
         </items>
      </SplitPane>
   </center>
</BorderPane>
