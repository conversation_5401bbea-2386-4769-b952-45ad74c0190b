package com.example.expense_backend.gui;

import com.example.expense_backend.entity.User;
import com.example.expense_backend.service.UserService;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.stage.Stage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URL;
import java.util.Optional;
import java.util.ResourceBundle;

@Component
public class LoginController implements Initializable {
    
    @FXML private TextField usernameField;
    @FXML private PasswordField passwordField;
    @FXML private Button loginButton;
    @FXML private Label statusLabel;
    @FXML private ComboBox<String> demoUserComboBox;
    
    private final UserService userService;
    private Stage primaryStage;
    
    @Autowired
    public LoginController(UserService userService) {
        this.userService = userService;
    }
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        setupEventHandlers();
        setupDemoUsers();
    }
    
    public void setPrimaryStage(Stage primaryStage) {
        this.primaryStage = primaryStage;
    }
    
    private void setupEventHandlers() {
        loginButton.setOnAction(e -> handleLogin());
        
        // Allow Enter key to trigger login
        usernameField.setOnAction(e -> handleLogin());
        passwordField.setOnAction(e -> handleLogin());
        
        // Demo user selection
        demoUserComboBox.setOnAction(e -> {
            String selectedUser = demoUserComboBox.getValue();
            if (selectedUser != null && !selectedUser.equals("Select Demo User...")) {
                String[] parts = selectedUser.split(" - ");
                if (parts.length >= 2) {
                    String username = parts[1].split(" \\(")[0];
                    usernameField.setText(username);
                    
                    // Set default password for demo users
                    if (username.equals("admin")) {
                        passwordField.setText("Admin123!");
                    } else if (username.contains("manager") || username.equals("jsmith") || username.equals("mjohnson")) {
                        passwordField.setText("Manager123!");
                    } else if (username.equals("bwilson")) {
                        passwordField.setText("Finance123!");
                    } else {
                        passwordField.setText("Employee123!");
                    }
                }
            }
        });
    }
    
    private void setupDemoUsers() {
        demoUserComboBox.getItems().addAll(
            "Select Demo User...",
            "Admin - admin (System Administrator)",
            "IT Manager - jsmith (John Smith)",
            "Sales Manager - mjohnson (Mary Johnson)", 
            "Finance Admin - bwilson (Bob Wilson)",
            "Employee (IT) - adavis (Alice Davis)",
            "Employee (Sales) - clee (Charlie Lee)"
        );
        demoUserComboBox.setValue("Select Demo User...");
    }
    
    private void handleLogin() {
        String username = usernameField.getText().trim();
        String password = passwordField.getText();
        
        if (username.isEmpty() || password.isEmpty()) {
            showStatus("Please enter both username and password.", true);
            return;
        }
        
        try {
            if (userService.authenticateUser(username, password)) {
                Optional<User> userOpt = userService.findByUsername(username);
                if (userOpt.isPresent()) {
                    User user = userOpt.get();
                    showStatus("Login successful! Welcome, " + user.getFullName(), false);
                    
                    // Load main application with user context
                    loadMainApplication(user);
                } else {
                    showStatus("User not found.", true);
                }
            } else {
                showStatus("Invalid username or password.", true);
            }
        } catch (Exception e) {
            showStatus("Login error: " + e.getMessage(), true);
            e.printStackTrace();
        }
    }
    
    private void loadMainApplication(User user) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/expense-main.fxml"));
            
            // Set the controller factory to use Spring's bean factory
            loader.setControllerFactory(clazz -> {
                if (clazz == ExpenseMainController.class) {
                    ExpenseMainController controller = (ExpenseMainController) 
                        primaryStage.getScene().getRoot().getScene().getWindow().getUserData();
                    if (controller == null) {
                        // Get controller from Spring context
                        try {
                            controller = org.springframework.context.ApplicationContextProvider.getApplicationContext()
                                    .getBean(ExpenseMainController.class);
                        } catch (Exception e) {
                            // Fallback - create new instance
                            controller = new ExpenseMainController(
                                org.springframework.context.ApplicationContextProvider.getApplicationContext()
                                    .getBean(com.example.expense_backend.service.ExpenseService.class),
                                org.springframework.context.ApplicationContextProvider.getApplicationContext()
                                    .getBean(UserService.class)
                            );
                        }
                    }
                    controller.setCurrentUser(user);
                    return controller;
                }
                return org.springframework.context.ApplicationContextProvider.getApplicationContext().getBean(clazz);
            });
            
            Parent root = loader.load();
            Scene scene = new Scene(root, 1400, 900);
            
            primaryStage.setTitle("Expense Management System - " + user.getFullName() + " (" + user.getRole().getDisplayName() + ")");
            primaryStage.setScene(scene);
            primaryStage.setMinWidth(1000);
            primaryStage.setMinHeight(700);
            
        } catch (Exception e) {
            showStatus("Error loading main application: " + e.getMessage(), true);
            e.printStackTrace();
        }
    }
    
    private void showStatus(String message, boolean isError) {
        statusLabel.setText(message);
        statusLabel.setStyle(isError ? "-fx-text-fill: red;" : "-fx-text-fill: green;");
        
        // Clear status after 5 seconds
        javafx.animation.Timeline timeline = new javafx.animation.Timeline(
            new javafx.animation.KeyFrame(javafx.util.Duration.seconds(5), e -> statusLabel.setText(""))
        );
        timeline.play();
    }
}
