package com.example.expense_backend.gui;

import com.example.expense_backend.entity.Expense;
import com.example.expense_backend.service.ExpenseService;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.stage.Stage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;

@Component
public class ExpenseMainController implements Initializable {
    
    @FXML private TableView<Expense> expenseTable;
    @FXML private TableColumn<Expense, Long> idColumn;
    @FXML private TableColumn<Expense, String> descriptionColumn;
    @FXML private TableColumn<Expense, BigDecimal> amountColumn;
    @FXML private TableColumn<Expense, String> dateColumn;
    @FXML private TableColumn<Expense, String> categoryColumn;
    @FXML private TableColumn<Expense, String> notesColumn;
    
    @FXML private TextField descriptionField;
    @FXML private TextField amountField;
    @FXML private DatePicker datePicker;
    @FXML private ComboBox<String> categoryComboBox;
    @FXML private TextArea notesArea;
    
    @FXML private Button addButton;
    @FXML private Button updateButton;
    @FXML private Button deleteButton;
    @FXML private Button clearButton;
    
    @FXML private TextField searchField;
    @FXML private ComboBox<String> filterCategoryComboBox;
    @FXML private DatePicker fromDatePicker;
    @FXML private DatePicker toDatePicker;
    @FXML private Button searchButton;
    @FXML private Button clearFiltersButton;
    
    @FXML private Label totalLabel;
    @FXML private Label countLabel;
    @FXML private Label averageLabel;
    
    private final ExpenseService expenseService;
    private final ObservableList<Expense> expenseList = FXCollections.observableArrayList();
    private Expense selectedExpense;
    
    @Autowired
    public ExpenseMainController(ExpenseService expenseService) {
        this.expenseService = expenseService;
    }
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        setupTableColumns();
        setupEventHandlers();
        loadCategories();
        loadExpenses();
        updateSummary();
    }
    
    private void setupTableColumns() {
        idColumn.setCellValueFactory(new PropertyValueFactory<>("id"));
        descriptionColumn.setCellValueFactory(new PropertyValueFactory<>("description"));
        amountColumn.setCellValueFactory(new PropertyValueFactory<>("amount"));
        
        // Format date column
        dateColumn.setCellValueFactory(cellData -> {
            LocalDate date = cellData.getValue().getDate();
            return new SimpleStringProperty(date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        });
        
        categoryColumn.setCellValueFactory(new PropertyValueFactory<>("category"));
        notesColumn.setCellValueFactory(new PropertyValueFactory<>("notes"));
        
        // Set table data
        expenseTable.setItems(expenseList);
        
        // Format amount column to show currency
        amountColumn.setCellFactory(column -> new TableCell<Expense, BigDecimal>() {
            @Override
            protected void updateItem(BigDecimal amount, boolean empty) {
                super.updateItem(amount, empty);
                if (empty || amount == null) {
                    setText(null);
                } else {
                    setText(String.format("$%.2f", amount));
                }
            }
        });
    }
    
    private void setupEventHandlers() {
        // Table selection handler
        expenseTable.getSelectionModel().selectedItemProperty().addListener(
            (observable, oldValue, newValue) -> {
                selectedExpense = newValue;
                populateForm(newValue);
                updateButton.setDisable(newValue == null);
                deleteButton.setDisable(newValue == null);
            }
        );
        
        // Button handlers
        addButton.setOnAction(e -> addExpense());
        updateButton.setOnAction(e -> updateExpense());
        deleteButton.setOnAction(e -> deleteExpense());
        clearButton.setOnAction(e -> clearForm());
        
        searchButton.setOnAction(e -> searchExpenses());
        clearFiltersButton.setOnAction(e -> clearFilters());
        
        // Set default date to today
        datePicker.setValue(LocalDate.now());
    }
    
    private void loadCategories() {
        List<String> categories = expenseService.getAllCategories();
        
        // Add default categories if none exist
        if (categories.isEmpty()) {
            categories = List.of("Food", "Transportation", "Entertainment", "Utilities", "Healthcare", "Shopping", "Other");
        }
        
        categoryComboBox.setItems(FXCollections.observableArrayList(categories));
        filterCategoryComboBox.setItems(FXCollections.observableArrayList(categories));
        
        // Add "All" option to filter combo box
        filterCategoryComboBox.getItems().add(0, "All Categories");
        filterCategoryComboBox.setValue("All Categories");
    }
    
    private void loadExpenses() {
        List<Expense> expenses = expenseService.findAllExpenses();
        expenseList.clear();
        expenseList.addAll(expenses);
        updateSummary();
    }
    
    private void populateForm(Expense expense) {
        if (expense == null) {
            clearForm();
            return;
        }
        
        descriptionField.setText(expense.getDescription());
        amountField.setText(expense.getAmount().toString());
        datePicker.setValue(expense.getDate());
        categoryComboBox.setValue(expense.getCategory());
        notesArea.setText(expense.getNotes());
    }
    
    private void clearForm() {
        descriptionField.clear();
        amountField.clear();
        datePicker.setValue(LocalDate.now());
        categoryComboBox.setValue(null);
        notesArea.clear();
        selectedExpense = null;
        updateButton.setDisable(true);
        deleteButton.setDisable(true);
    }
    
    private void addExpense() {
        try {
            Expense expense = createExpenseFromForm();
            if (expenseService.isValidExpense(expense)) {
                expenseService.saveExpense(expense);
                loadExpenses();
                loadCategories(); // Refresh categories in case a new one was added
                clearForm();
                showAlert("Success", "Expense added successfully!", Alert.AlertType.INFORMATION);
            } else {
                showAlert("Error", "Please fill in all required fields with valid data.", Alert.AlertType.ERROR);
            }
        } catch (Exception e) {
            showAlert("Error", "Error adding expense: " + e.getMessage(), Alert.AlertType.ERROR);
        }
    }
    
    private void updateExpense() {
        if (selectedExpense == null) return;
        
        try {
            Expense updatedExpense = createExpenseFromForm();
            updatedExpense.setId(selectedExpense.getId());
            updatedExpense.setCreatedAt(selectedExpense.getCreatedAt());
            
            if (expenseService.isValidExpense(updatedExpense)) {
                expenseService.updateExpense(updatedExpense);
                loadExpenses();
                loadCategories();
                clearForm();
                showAlert("Success", "Expense updated successfully!", Alert.AlertType.INFORMATION);
            } else {
                showAlert("Error", "Please fill in all required fields with valid data.", Alert.AlertType.ERROR);
            }
        } catch (Exception e) {
            showAlert("Error", "Error updating expense: " + e.getMessage(), Alert.AlertType.ERROR);
        }
    }
    
    private void deleteExpense() {
        if (selectedExpense == null) return;
        
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("Confirm Delete");
        confirmAlert.setHeaderText("Delete Expense");
        confirmAlert.setContentText("Are you sure you want to delete this expense?");
        
        Optional<ButtonType> result = confirmAlert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            try {
                expenseService.deleteExpense(selectedExpense.getId());
                loadExpenses();
                clearForm();
                showAlert("Success", "Expense deleted successfully!", Alert.AlertType.INFORMATION);
            } catch (Exception e) {
                showAlert("Error", "Error deleting expense: " + e.getMessage(), Alert.AlertType.ERROR);
            }
        }
    }
    
    private Expense createExpenseFromForm() {
        Expense expense = new Expense();
        expense.setDescription(descriptionField.getText().trim());
        expense.setAmount(new BigDecimal(amountField.getText().trim()));
        expense.setDate(datePicker.getValue());
        expense.setCategory(categoryComboBox.getValue());
        expense.setNotes(notesArea.getText().trim());
        return expense;
    }
    
    private void searchExpenses() {
        String searchTerm = searchField.getText().trim();
        String selectedCategory = filterCategoryComboBox.getValue();
        LocalDate fromDate = fromDatePicker.getValue();
        LocalDate toDate = toDatePicker.getValue();
        
        List<Expense> filteredExpenses;
        
        if (!searchTerm.isEmpty()) {
            filteredExpenses = expenseService.searchExpensesByDescription(searchTerm);
        } else if (selectedCategory != null && !selectedCategory.equals("All Categories")) {
            if (fromDate != null && toDate != null) {
                filteredExpenses = expenseService.findExpensesByCategoryAndDateRange(selectedCategory, fromDate, toDate);
            } else {
                filteredExpenses = expenseService.findExpensesByCategory(selectedCategory);
            }
        } else if (fromDate != null && toDate != null) {
            filteredExpenses = expenseService.findExpensesByDateRange(fromDate, toDate);
        } else {
            filteredExpenses = expenseService.findAllExpenses();
        }
        
        expenseList.clear();
        expenseList.addAll(filteredExpenses);
        updateSummary();
    }
    
    private void clearFilters() {
        searchField.clear();
        filterCategoryComboBox.setValue("All Categories");
        fromDatePicker.setValue(null);
        toDatePicker.setValue(null);
        loadExpenses();
    }
    
    private void updateSummary() {
        if (expenseList.isEmpty()) {
            totalLabel.setText("Total: $0.00");
            countLabel.setText("Count: 0");
            averageLabel.setText("Average: $0.00");
            return;
        }
        
        BigDecimal total = expenseList.stream()
                .map(Expense::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        BigDecimal average = total.divide(BigDecimal.valueOf(expenseList.size()), 2, RoundingMode.HALF_UP);
        
        totalLabel.setText(String.format("Total: $%.2f", total));
        countLabel.setText("Count: " + expenseList.size());
        averageLabel.setText(String.format("Average: $%.2f", average));
    }
    
    private void showAlert(String title, String message, Alert.AlertType type) {
        Alert alert = new Alert(type);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
}
