package com.example.expense_backend.gui;

import com.example.expense_backend.entity.Expense;
import com.example.expense_backend.entity.ExpenseStatus;
import com.example.expense_backend.entity.User;
import com.example.expense_backend.entity.UserRole;
import com.example.expense_backend.service.ExpenseService;
import com.example.expense_backend.service.UserService;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;

@Component
public class ExpenseMainController implements Initializable {

    @FXML private TableView<Expense> expenseTable;
    @FXML private TableColumn<Expense, String> expenseNumberColumn;
    @FXML private TableColumn<Expense, String> descriptionColumn;
    @FXML private TableColumn<Expense, BigDecimal> amountColumn;
    @FXML private TableColumn<Expense, String> dateColumn;
    @FXML private TableColumn<Expense, String> categoryColumn;
    @FXML private TableColumn<Expense, String> statusColumn;
    @FXML private TableColumn<Expense, String> businessPurposeColumn;
    @FXML private TableColumn<Expense, String> submittedByColumn;

    @FXML private TextField descriptionField;
    @FXML private TextField amountField;
    @FXML private DatePicker datePicker;
    @FXML private ComboBox<String> categoryComboBox;
    @FXML private TextArea notesArea;
    @FXML private TextField businessPurposeField;
    @FXML private TextField projectCodeField;
    @FXML private TextField costCenterField;
    @FXML private CheckBox billableCheckBox;
    @FXML private TextField clientNameField;

    @FXML private Button addButton;
    @FXML private Button updateButton;
    @FXML private Button deleteButton;
    @FXML private Button clearButton;
    @FXML private Button submitButton;
    @FXML private Button approveButton;
    @FXML private Button rejectButton;

    @FXML private TextField searchField;
    @FXML private ComboBox<String> filterCategoryComboBox;
    @FXML private ComboBox<String> filterStatusComboBox;
    @FXML private DatePicker fromDatePicker;
    @FXML private DatePicker toDatePicker;
    @FXML private Button searchButton;
    @FXML private Button clearFiltersButton;

    @FXML private Label totalLabel;
    @FXML private Label countLabel;
    @FXML private Label averageLabel;
    @FXML private Label userInfoLabel;
    @FXML private Label organizationLabel;

    private final ExpenseService expenseService;
    private final UserService userService;
    private final ObservableList<Expense> expenseList = FXCollections.observableArrayList();
    private Expense selectedExpense;
    private User currentUser;
    
    @Autowired
    public ExpenseMainController(ExpenseService expenseService, UserService userService) {
        this.expenseService = expenseService;
        this.userService = userService;
    }

    public void setCurrentUser(User user) {
        this.currentUser = user;
        updateUserInterface();
    }
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        try {
            System.out.println("Initializing ExpenseMainController...");
            setupTableColumns();
            setupEventHandlers();
            loadCategories();
            // Don't load expenses yet - wait for user to be set
            System.out.println("ExpenseMainController initialized successfully");
        } catch (Exception e) {
            System.err.println("Error initializing ExpenseMainController: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void setupTableColumns() {
        expenseNumberColumn.setCellValueFactory(new PropertyValueFactory<>("expenseNumber"));
        descriptionColumn.setCellValueFactory(new PropertyValueFactory<>("description"));
        amountColumn.setCellValueFactory(new PropertyValueFactory<>("amount"));

        // Format date column
        dateColumn.setCellValueFactory(cellData -> {
            LocalDate date = cellData.getValue().getDate();
            return new SimpleStringProperty(date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        });

        categoryColumn.setCellValueFactory(new PropertyValueFactory<>("category"));

        // Status column
        statusColumn.setCellValueFactory(cellData -> {
            ExpenseStatus status = cellData.getValue().getStatus();
            return new SimpleStringProperty(status.getDisplayName());
        });

        // Business purpose column
        businessPurposeColumn.setCellValueFactory(new PropertyValueFactory<>("businessPurpose"));

        // Submitted by column
        submittedByColumn.setCellValueFactory(cellData -> {
            User user = cellData.getValue().getUser();
            return new SimpleStringProperty(user != null ? user.getFullName() : "");
        });
        
        // Set table data
        expenseTable.setItems(expenseList);
        
        // Format amount column to show currency
        amountColumn.setCellFactory(column -> new TableCell<Expense, BigDecimal>() {
            @Override
            protected void updateItem(BigDecimal amount, boolean empty) {
                super.updateItem(amount, empty);
                if (empty || amount == null) {
                    setText(null);
                } else {
                    setText(String.format("$%.2f", amount));
                }
            }
        });
    }
    
    private void setupEventHandlers() {
        // Table selection handler
        expenseTable.getSelectionModel().selectedItemProperty().addListener(
            (observable, oldValue, newValue) -> {
                selectedExpense = newValue;
                populateForm(newValue);
                updateButton.setDisable(newValue == null);
                deleteButton.setDisable(newValue == null);
            }
        );
        
        // Button handlers
        addButton.setOnAction(e -> addExpense());
        updateButton.setOnAction(e -> updateExpense());
        deleteButton.setOnAction(e -> deleteExpense());
        clearButton.setOnAction(e -> clearForm());
        
        searchButton.setOnAction(e -> searchExpenses());
        clearFiltersButton.setOnAction(e -> clearFilters());
        
        // Set default date to today
        datePicker.setValue(LocalDate.now());

        // Setup approval buttons
        submitButton.setOnAction(e -> submitExpense());
        approveButton.setOnAction(e -> approveExpense());
        rejectButton.setOnAction(e -> rejectExpense());
    }

    private void updateUserInterface() {
        if (currentUser != null) {
            System.out.println("Updating UI for user: " + currentUser.getFullName());
            userInfoLabel.setText(currentUser.getFullName() + " (" + currentUser.getRole().getDisplayName() + ")");

            try {
                organizationLabel.setText(currentUser.getOrganization().getName());
            } catch (Exception e) {
                organizationLabel.setText("Demo Corporation");
            }

            // Show/hide buttons based on user role
            boolean canApprove = currentUser.canApproveExpenses();
            approveButton.setVisible(canApprove);
            rejectButton.setVisible(canApprove);

            // Load user's expenses or all expenses based on role
            loadExpenses();
            updateSummary();
        }
    }
    
    private void loadCategories() {
        List<String> categories = expenseService.getAllCategories();

        // Add default categories if none exist
        if (categories.isEmpty()) {
            categories = List.of("Food", "Transportation", "Entertainment", "Utilities", "Healthcare", "Shopping", "Software", "Office Supplies", "Travel", "Meals & Entertainment", "Other");
        }

        categoryComboBox.setItems(FXCollections.observableArrayList(categories));
        filterCategoryComboBox.setItems(FXCollections.observableArrayList(categories));

        // Add "All" option to filter combo box
        filterCategoryComboBox.getItems().add(0, "All Categories");
        filterCategoryComboBox.setValue("All Categories");

        // Setup status filter
        filterStatusComboBox.setItems(FXCollections.observableArrayList(
            "All Statuses", "Draft", "Submitted", "Under Review", "Approved", "Rejected", "Paid"
        ));
        filterStatusComboBox.setValue("All Statuses");
    }
    
    private void loadExpenses() {
        List<Expense> expenses = expenseService.findAllExpenses();
        expenseList.clear();
        expenseList.addAll(expenses);
        updateSummary();
    }
    
    private void populateForm(Expense expense) {
        if (expense == null) {
            clearForm();
            return;
        }

        descriptionField.setText(expense.getDescription());
        amountField.setText(expense.getAmount().toString());
        datePicker.setValue(expense.getDate());
        categoryComboBox.setValue(expense.getCategory());
        notesArea.setText(expense.getNotes());
        businessPurposeField.setText(expense.getBusinessPurpose());
        projectCodeField.setText(expense.getProjectCode());
        costCenterField.setText(expense.getCostCenter());
        billableCheckBox.setSelected(expense.isBillable());
        clientNameField.setText(expense.getClientName());

        // Update button states based on expense status and user permissions
        updateButtonStates(expense);
    }
    
    private void clearForm() {
        descriptionField.clear();
        amountField.clear();
        datePicker.setValue(LocalDate.now());
        categoryComboBox.setValue(null);
        notesArea.clear();
        businessPurposeField.clear();
        projectCodeField.clear();
        costCenterField.clear();
        billableCheckBox.setSelected(false);
        clientNameField.clear();
        selectedExpense = null;
        updateButton.setDisable(true);
        deleteButton.setDisable(true);
        submitButton.setDisable(true);
        approveButton.setDisable(true);
        rejectButton.setDisable(true);
    }

    private void updateButtonStates(Expense expense) {
        if (expense == null || currentUser == null) {
            updateButton.setDisable(true);
            deleteButton.setDisable(true);
            submitButton.setDisable(true);
            approveButton.setDisable(true);
            rejectButton.setDisable(true);
            return;
        }

        boolean isOwner = expense.getUser().getId().equals(currentUser.getId());
        boolean canApprove = currentUser.canApproveExpenses();
        boolean canEdit = expense.canBeEdited() && isOwner;

        updateButton.setDisable(!canEdit);
        deleteButton.setDisable(!canEdit);
        submitButton.setDisable(!canEdit || expense.getStatus() != ExpenseStatus.DRAFT);
        approveButton.setDisable(!canApprove || !expense.getStatus().canBeApproved());
        rejectButton.setDisable(!canApprove || !expense.getStatus().canBeRejected());
    }
    
    private void addExpense() {
        try {
            Expense expense = createExpenseFromForm();
            if (expenseService.isValidExpense(expense)) {
                expenseService.saveExpense(expense);
                loadExpenses();
                loadCategories(); // Refresh categories in case a new one was added
                clearForm();
                showAlert("Success", "Expense added successfully!", Alert.AlertType.INFORMATION);
            } else {
                showAlert("Error", "Please fill in all required fields with valid data.", Alert.AlertType.ERROR);
            }
        } catch (Exception e) {
            showErrorWithDetails("Error Adding Expense", "Failed to add expense", e);
        }
    }
    
    private void updateExpense() {
        if (selectedExpense == null) return;
        
        try {
            Expense updatedExpense = createExpenseFromForm();
            updatedExpense.setId(selectedExpense.getId());
            updatedExpense.setCreatedAt(selectedExpense.getCreatedAt());
            
            if (expenseService.isValidExpense(updatedExpense)) {
                expenseService.updateExpense(updatedExpense);
                loadExpenses();
                loadCategories();
                clearForm();
                showAlert("Success", "Expense updated successfully!", Alert.AlertType.INFORMATION);
            } else {
                showAlert("Error", "Please fill in all required fields with valid data.", Alert.AlertType.ERROR);
            }
        } catch (Exception e) {
            showErrorWithDetails("Error Updating Expense", "Failed to update expense", e);
        }
    }
    
    private void deleteExpense() {
        if (selectedExpense == null) return;
        
        Alert confirmAlert = new Alert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("Confirm Delete");
        confirmAlert.setHeaderText("Delete Expense");
        confirmAlert.setContentText("Are you sure you want to delete this expense?");
        
        Optional<ButtonType> result = confirmAlert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            try {
                expenseService.deleteExpense(selectedExpense.getId());
                loadExpenses();
                clearForm();
                showAlert("Success", "Expense deleted successfully!", Alert.AlertType.INFORMATION);
            } catch (Exception e) {
                showAlert("Error", "Error deleting expense: " + e.getMessage(), Alert.AlertType.ERROR);
            }
        }
    }
    
    private Expense createExpenseFromForm() {
        Expense expense = new Expense();
        expense.setDescription(descriptionField.getText().trim());
        expense.setAmount(new BigDecimal(amountField.getText().trim()));
        expense.setDate(datePicker.getValue());
        expense.setCategory(categoryComboBox.getValue());
        expense.setNotes(notesArea.getText().trim());
        expense.setBusinessPurpose(businessPurposeField.getText().trim());
        expense.setProjectCode(projectCodeField.getText().trim());
        expense.setCostCenter(costCenterField.getText().trim());
        expense.setBillable(billableCheckBox.isSelected());
        expense.setClientName(clientNameField.getText().trim());
        expense.setUser(currentUser);
        return expense;
    }
    
    private void searchExpenses() {
        String searchTerm = searchField.getText().trim();
        String selectedCategory = filterCategoryComboBox.getValue();
        LocalDate fromDate = fromDatePicker.getValue();
        LocalDate toDate = toDatePicker.getValue();
        
        List<Expense> filteredExpenses;
        
        if (!searchTerm.isEmpty()) {
            filteredExpenses = expenseService.searchExpensesByDescription(searchTerm);
        } else if (selectedCategory != null && !selectedCategory.equals("All Categories")) {
            if (fromDate != null && toDate != null) {
                filteredExpenses = expenseService.findExpensesByCategoryAndDateRange(selectedCategory, fromDate, toDate);
            } else {
                filteredExpenses = expenseService.findExpensesByCategory(selectedCategory);
            }
        } else if (fromDate != null && toDate != null) {
            filteredExpenses = expenseService.findExpensesByDateRange(fromDate, toDate);
        } else {
            filteredExpenses = expenseService.findAllExpenses();
        }
        
        expenseList.clear();
        expenseList.addAll(filteredExpenses);
        updateSummary();
    }
    
    private void clearFilters() {
        searchField.clear();
        filterCategoryComboBox.setValue("All Categories");
        fromDatePicker.setValue(null);
        toDatePicker.setValue(null);
        loadExpenses();
    }
    
    private void updateSummary() {
        if (expenseList.isEmpty()) {
            totalLabel.setText("Total: $0.00");
            countLabel.setText("Count: 0");
            averageLabel.setText("Average: $0.00");
            return;
        }
        
        BigDecimal total = expenseList.stream()
                .map(Expense::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        BigDecimal average = total.divide(BigDecimal.valueOf(expenseList.size()), 2, RoundingMode.HALF_UP);
        
        totalLabel.setText(String.format("Total: $%.2f", total));
        countLabel.setText("Count: " + expenseList.size());
        averageLabel.setText(String.format("Average: $%.2f", average));
    }
    
    private void showAlert(String title, String message, Alert.AlertType type) {
        Alert alert = new Alert(type);
        alert.setTitle(title);
        alert.setHeaderText(null);

        // For error messages, make them copyable
        if (type == Alert.AlertType.ERROR) {
            alert.setContentText(null);

            // Create a text area for copyable error text
            TextArea textArea = new TextArea(message);
            textArea.setEditable(false);
            textArea.setWrapText(true);
            textArea.setMaxWidth(Double.MAX_VALUE);
            textArea.setMaxHeight(Double.MAX_VALUE);
            textArea.setPrefRowCount(10);
            textArea.setPrefColumnCount(60);

            // Make it look like a label but still copyable
            textArea.setStyle("-fx-background-color: transparent; -fx-border-color: transparent;");

            // Set the expandable Exception into the dialog pane
            alert.getDialogPane().setExpandableContent(textArea);
            alert.getDialogPane().setExpanded(true);

            // Also set a shorter content text
            String shortMessage = message.length() > 100 ? message.substring(0, 100) + "..." : message;
            alert.setContentText(shortMessage + "\n\n(Click 'Show Details' for full copyable error)");
        } else {
            alert.setContentText(message);
        }

        alert.showAndWait();
    }

    private void showErrorWithDetails(String title, String message, Exception exception) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle(title);
        alert.setHeaderText(message);

        // Create expandable Exception content
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        exception.printStackTrace(pw);
        String exceptionText = sw.toString();

        Label label = new Label("Full error details:");

        TextArea textArea = new TextArea(exceptionText);
        textArea.setEditable(false);
        textArea.setWrapText(true);
        textArea.setMaxWidth(Double.MAX_VALUE);
        textArea.setMaxHeight(Double.MAX_VALUE);
        textArea.setPrefRowCount(15);
        textArea.setPrefColumnCount(80);

        VBox expContent = new VBox();
        expContent.getChildren().addAll(label, textArea);

        // Set expandable Exception into the dialog pane
        alert.getDialogPane().setExpandableContent(expContent);

        alert.showAndWait();
    }

    // Workflow methods
    private void submitExpense() {
        if (selectedExpense == null) return;

        try {
            selectedExpense.submit();
            expenseService.updateExpense(selectedExpense);
            loadExpenses();
            updateButtonStates(selectedExpense);
            showAlert("Success", "Expense submitted for approval!", Alert.AlertType.INFORMATION);
        } catch (Exception e) {
            showErrorWithDetails("Error Submitting Expense", "Failed to submit expense for approval", e);
        }
    }

    private void approveExpense() {
        if (selectedExpense == null || !currentUser.canApproveExpenses()) return;

        TextInputDialog dialog = new TextInputDialog();
        dialog.setTitle("Approve Expense");
        dialog.setHeaderText("Approve expense: " + selectedExpense.getDescription());
        dialog.setContentText("Comments (optional):");

        Optional<String> result = dialog.showAndWait();
        if (result.isPresent()) {
            try {
                selectedExpense.approve(currentUser);
                expenseService.updateExpense(selectedExpense);
                loadExpenses();
                updateButtonStates(selectedExpense);
                showAlert("Success", "Expense approved successfully!", Alert.AlertType.INFORMATION);
            } catch (Exception e) {
                showAlert("Error", "Error approving expense: " + e.getMessage(), Alert.AlertType.ERROR);
            }
        }
    }

    private void rejectExpense() {
        if (selectedExpense == null || !currentUser.canApproveExpenses()) return;

        TextInputDialog dialog = new TextInputDialog();
        dialog.setTitle("Reject Expense");
        dialog.setHeaderText("Reject expense: " + selectedExpense.getDescription());
        dialog.setContentText("Rejection reason:");

        Optional<String> result = dialog.showAndWait();
        if (result.isPresent() && !result.get().trim().isEmpty()) {
            try {
                selectedExpense.reject(result.get().trim());
                expenseService.updateExpense(selectedExpense);
                loadExpenses();
                updateButtonStates(selectedExpense);
                showAlert("Success", "Expense rejected.", Alert.AlertType.INFORMATION);
            } catch (Exception e) {
                showAlert("Error", "Error rejecting expense: " + e.getMessage(), Alert.AlertType.ERROR);
            }
        } else {
            showAlert("Error", "Rejection reason is required.", Alert.AlertType.ERROR);
        }
    }
}
