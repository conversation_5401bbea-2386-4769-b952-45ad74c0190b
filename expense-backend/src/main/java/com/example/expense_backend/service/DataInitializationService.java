package com.example.expense_backend.service;

import com.example.expense_backend.entity.*;
import com.example.expense_backend.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;

@Service
public class DataInitializationService implements CommandLineRunner {
    
    private final OrganizationService organizationService;
    private final UserService userService;
    private final DepartmentRepository departmentRepository;
    private final ExpenseService expenseService;
    
    @Autowired
    public DataInitializationService(OrganizationService organizationService,
                                   UserService userService,
                                   DepartmentRepository departmentRepository,
                                   ExpenseService expenseService) {
        this.organizationService = organizationService;
        this.userService = userService;
        this.departmentRepository = departmentRepository;
        this.expenseService = expenseService;
    }
    
    @Override
    public void run(String... args) throws Exception {
        initializeSampleData();
    }
    
    private void initializeSampleData() {
        try {
            // Check if data already exists
            if (!organizationService.findAllActive().isEmpty()) {
                System.out.println("Sample data already exists, skipping initialization.");
                return;
            }
            
            System.out.println("Initializing sample data...");
            
            // Create sample organization
            Organization org = new Organization();
            org.setTenantId("demo-org");
            org.setName("Demo Corporation");
            org.setDomain("democorp.com");
            org.setContactEmail("<EMAIL>");
            org.setSubscriptionPlan(SubscriptionPlan.PROFESSIONAL);
            org.setDefaultCurrency("USD");
            org.setExpenseApprovalRequired(true);
            org.setAutoApproveLimit(new BigDecimal("100.00"));
            
            Organization savedOrg = organizationService.createOrganization(org);
            
            // Create departments
            Department itDept = new Department("Information Technology", "IT", savedOrg);
            itDept.setBudgetCode("IT-001");
            itDept.setCostCenter("CC-IT");
            itDept.setMonthlyBudget(new BigDecimal("10000.00"));
            itDept.setAnnualBudget(new BigDecimal("120000.00"));
            Department savedItDept = departmentRepository.save(itDept);
            
            Department salesDept = new Department("Sales", "SALES", savedOrg);
            salesDept.setBudgetCode("SALES-001");
            salesDept.setCostCenter("CC-SALES");
            salesDept.setMonthlyBudget(new BigDecimal("15000.00"));
            salesDept.setAnnualBudget(new BigDecimal("180000.00"));
            Department savedSalesDept = departmentRepository.save(salesDept);
            
            Department hrDept = new Department("Human Resources", "HR", savedOrg);
            hrDept.setBudgetCode("HR-001");
            hrDept.setCostCenter("CC-HR");
            hrDept.setMonthlyBudget(new BigDecimal("5000.00"));
            hrDept.setAnnualBudget(new BigDecimal("60000.00"));
            Department savedHrDept = departmentRepository.save(hrDept);
            
            // Create admin user
            User admin = new User("admin", "<EMAIL>", "System", "Administrator", savedOrg);
            admin.setRole(UserRole.ADMIN);
            admin.setEmployeeId("EMP-001");
            admin.setJobTitle("System Administrator");
            admin.setSpendingLimit(new BigDecimal("5000.00"));
            admin.setDepartment(savedItDept);
            User savedAdmin = userService.createUser(admin, "Admin123!");
            
            // Create IT manager
            User itManager = new User("jsmith", "<EMAIL>", "John", "Smith", savedOrg);
            itManager.setRole(UserRole.MANAGER);
            itManager.setEmployeeId("EMP-002");
            itManager.setJobTitle("IT Manager");
            itManager.setSpendingLimit(new BigDecimal("2000.00"));
            itManager.setDepartment(savedItDept);
            User savedItManager = userService.createUser(itManager, "Manager123!");
            
            // Set IT department head
            savedItDept.setDepartmentHead(savedItManager);
            departmentRepository.save(savedItDept);
            
            // Create Sales manager
            User salesManager = new User("mjohnson", "<EMAIL>", "Mary", "Johnson", savedOrg);
            salesManager.setRole(UserRole.MANAGER);
            salesManager.setEmployeeId("EMP-003");
            salesManager.setJobTitle("Sales Manager");
            salesManager.setSpendingLimit(new BigDecimal("3000.00"));
            salesManager.setDepartment(savedSalesDept);
            User savedSalesManager = userService.createUser(salesManager, "Manager123!");
            
            // Set Sales department head
            savedSalesDept.setDepartmentHead(savedSalesManager);
            departmentRepository.save(savedSalesDept);
            
            // Create Finance Admin
            User financeAdmin = new User("bwilson", "<EMAIL>", "Bob", "Wilson", savedOrg);
            financeAdmin.setRole(UserRole.FINANCE_ADMIN);
            financeAdmin.setEmployeeId("EMP-004");
            financeAdmin.setJobTitle("Finance Administrator");
            financeAdmin.setSpendingLimit(new BigDecimal("10000.00"));
            financeAdmin.setDepartment(savedHrDept);
            User savedFinanceAdmin = userService.createUser(financeAdmin, "Finance123!");
            
            // Create regular employees
            User employee1 = new User("adavis", "<EMAIL>", "Alice", "Davis", savedOrg);
            employee1.setRole(UserRole.EMPLOYEE);
            employee1.setEmployeeId("EMP-005");
            employee1.setJobTitle("Software Developer");
            employee1.setSpendingLimit(new BigDecimal("500.00"));
            employee1.setDepartment(savedItDept);
            employee1.setManager(savedItManager);
            User savedEmployee1 = userService.createUser(employee1, "Employee123!");
            
            User employee2 = new User("clee", "<EMAIL>", "Charlie", "Lee", savedOrg);
            employee2.setRole(UserRole.EMPLOYEE);
            employee2.setEmployeeId("EMP-006");
            employee2.setJobTitle("Sales Representative");
            employee2.setSpendingLimit(new BigDecimal("1000.00"));
            employee2.setDepartment(savedSalesDept);
            employee2.setManager(savedSalesManager);
            User savedEmployee2 = userService.createUser(employee2, "Employee123!");
            
            // Create sample expenses
            createSampleExpenses(savedEmployee1, savedEmployee2);
            
            System.out.println("Sample data initialization completed successfully!");
            System.out.println("=== Login Credentials ===");
            System.out.println("Admin: admin / Admin123!");
            System.out.println("IT Manager: jsmith / Manager123!");
            System.out.println("Sales Manager: mjohnson / Manager123!");
            System.out.println("Finance Admin: bwilson / Finance123!");
            System.out.println("Employee (IT): adavis / Employee123!");
            System.out.println("Employee (Sales): clee / Employee123!");
            
        } catch (Exception e) {
            System.err.println("Error initializing sample data: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void createSampleExpenses(User employee1, User employee2) {
        // Sample expenses for IT employee
        Expense expense1 = new Expense("Office supplies", new BigDecimal("45.99"), 
                                     LocalDate.now().minusDays(5), "Office Supplies", employee1);
        expense1.setBusinessPurpose("Monthly office supplies for development team");
        expense1.setProjectCode("PROJ-001");
        expense1.setCostCenter("CC-IT");
        expenseService.saveExpense(expense1);
        
        Expense expense2 = new Expense("Software license", new BigDecimal("299.00"), 
                                     LocalDate.now().minusDays(3), "Software", employee1);
        expense2.setBusinessPurpose("IntelliJ IDEA license renewal");
        expense2.setProjectCode("PROJ-001");
        expense2.setCostCenter("CC-IT");
        expense2.submit();
        expenseService.saveExpense(expense2);
        
        // Sample expenses for Sales employee
        Expense expense3 = new Expense("Client lunch", new BigDecimal("125.50"), 
                                     LocalDate.now().minusDays(2), "Meals & Entertainment", employee2);
        expense3.setBusinessPurpose("Client meeting with ABC Corp");
        expense3.setClientName("ABC Corp");
        expense3.setBillable(true);
        expense3.setCostCenter("CC-SALES");
        expenseService.saveExpense(expense3);
        
        Expense expense4 = new Expense("Travel - Conference", new BigDecimal("850.00"), 
                                     LocalDate.now().minusDays(1), "Travel", employee2);
        expense4.setBusinessPurpose("Sales conference attendance");
        expense4.setCostCenter("CC-SALES");
        expense4.submit();
        expenseService.saveExpense(expense4);
    }
}
