package com.example.expense_backend.service;

import com.example.expense_backend.entity.Expense;
import com.example.expense_backend.entity.ExpenseStatus;
import com.example.expense_backend.entity.User;
import com.example.expense_backend.repository.ExpenseRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class ExpenseService {
    
    private final ExpenseRepository expenseRepository;
    
    @Autowired
    public ExpenseService(ExpenseRepository expenseRepository) {
        this.expenseRepository = expenseRepository;
    }
    
    // Basic CRUD operations
    public Expense saveExpense(Expense expense) {
        return expenseRepository.save(expense);
    }
    
    public Optional<Expense> findById(Long id) {
        return expenseRepository.findById(id);
    }
    
    public List<Expense> findAllExpenses() {
        return expenseRepository.findAll();
    }
    
    public void deleteExpense(Long id) {
        expenseRepository.deleteById(id);
    }
    
    public Expense updateExpense(Expense expense) {
        return expenseRepository.save(expense);
    }
    
    // Business logic methods
    public List<Expense> findExpensesByCategory(String category) {
        return expenseRepository.findByCategory(category);
    }
    
    public List<Expense> findExpensesByDateRange(LocalDate startDate, LocalDate endDate) {
        return expenseRepository.findByDateBetween(startDate, endDate);
    }
    
    public List<Expense> findExpensesByCategoryAndDateRange(String category, LocalDate startDate, LocalDate endDate) {
        return expenseRepository.findByCategoryAndDateBetween(category, startDate, endDate);
    }
    
    public List<Expense> searchExpensesByDescription(String searchTerm) {
        return expenseRepository.findByDescriptionContainingIgnoreCase(searchTerm);
    }
    
    public List<String> getAllCategories() {
        return expenseRepository.findDistinctCategories();
    }
    
    public List<Expense> getCurrentMonthExpenses() {
        return expenseRepository.findCurrentMonthExpenses();
    }
    
    public List<Expense> getCurrentYearExpenses() {
        return expenseRepository.findCurrentYearExpenses();
    }
    
    public List<Expense> getRecentExpenses(int days) {
        LocalDate fromDate = LocalDate.now().minusDays(days);
        return expenseRepository.findRecentExpenses(fromDate);
    }
    
    public List<Expense> getTopExpenses() {
        return expenseRepository.findTop10ByOrderByAmountDesc();
    }
    
    // Summary and analytics methods
    public BigDecimal getTotalExpensesForDateRange(LocalDate startDate, LocalDate endDate) {
        BigDecimal total = expenseRepository.getTotalAmountByDateRange(startDate, endDate);
        return total != null ? total : BigDecimal.ZERO;
    }
    
    public BigDecimal getTotalExpensesForCurrentMonth() {
        YearMonth currentMonth = YearMonth.now();
        LocalDate startDate = currentMonth.atDay(1);
        LocalDate endDate = currentMonth.atEndOfMonth();
        return getTotalExpensesForDateRange(startDate, endDate);
    }
    
    public BigDecimal getTotalExpensesForCurrentYear() {
        LocalDate startDate = LocalDate.of(LocalDate.now().getYear(), 1, 1);
        LocalDate endDate = LocalDate.of(LocalDate.now().getYear(), 12, 31);
        return getTotalExpensesForDateRange(startDate, endDate);
    }
    
    public List<Object[]> getExpensesByCategory() {
        return expenseRepository.getTotalAmountByCategory();
    }
    
    // Validation methods
    public boolean isValidExpense(Expense expense) {
        return expense != null &&
               expense.getDescription() != null && !expense.getDescription().trim().isEmpty() &&
               expense.getAmount() != null && expense.getAmount().compareTo(BigDecimal.ZERO) > 0 &&
               expense.getDate() != null &&
               expense.getCategory() != null && !expense.getCategory().trim().isEmpty();
    }
    
    // Utility methods
    public long getTotalExpenseCount() {
        return expenseRepository.count();
    }
    
    public BigDecimal getAverageExpenseAmount() {
        List<Expense> allExpenses = expenseRepository.findAll();
        if (allExpenses.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal total = allExpenses.stream()
                .map(Expense::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        return total.divide(BigDecimal.valueOf(allExpenses.size()), 2, RoundingMode.HALF_UP);
    }
    
    public List<Expense> findExpensesAboveAmount(BigDecimal amount) {
        return expenseRepository.findByAmountGreaterThan(amount);
    }
}
