package com.example.expense_backend.service;

import com.example.expense_backend.entity.Expense;
import com.example.expense_backend.entity.ExpenseStatus;
import com.example.expense_backend.entity.User;
import com.example.expense_backend.repository.ExpenseRepository;
import com.example.expense_backend.service.rules.RuleResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class ExpenseService {

    private static final Logger logger = LoggerFactory.getLogger(ExpenseService.class);

    private final ExpenseRepository expenseRepository;
    // Temporarily comment out rules engine to get app working
    // private final EnterpriseRulesEngine rulesEngine;

    @Autowired
    public ExpenseService(ExpenseRepository expenseRepository) {
        this.expenseRepository = expenseRepository;
        // this.rulesEngine = rulesEngine;
    }
    
    // Basic CRUD operations
    public Expense saveExpense(Expense expense) {
        return expenseRepository.save(expense);
    }

    /**
     * Enterprise method: Save expense with business rules validation
     *
     * This method demonstrates enterprise patterns:
     * - Business rules validation before persistence
     * - Comprehensive rule result handling
     * - Audit trail generation
     * - Decision tracking
     *
     * TODO: Re-enable when Drools rules are fixed
     */
    public ExpenseValidationResult saveExpenseWithValidation(Expense expense) {
        logger.info("Saving expense with enterprise validation: {}", expense.getId());

        // TODO: Execute business rules when Drools is working
        // RuleResult ruleResult = rulesEngine.executeAllRules(expense);

        // For now, create a simple validation result
        RuleResult ruleResult = new RuleResult();
        ExpenseValidationResult validationResult = new ExpenseValidationResult(expense, ruleResult);

        // Save the expense (no rules validation for now)
        Expense savedExpense = expenseRepository.save(expense);
        validationResult.setSavedExpense(savedExpense);
        logger.info("Expense {} saved successfully (rules engine disabled)", savedExpense.getId());

        return validationResult;
    }
    
    public Optional<Expense> findById(Long id) {
        return expenseRepository.findById(id);
    }
    
    public List<Expense> findAllExpenses() {
        return expenseRepository.findAllWithUser();
    }
    
    public void deleteExpense(Long id) {
        expenseRepository.deleteById(id);
    }
    
    public Expense updateExpense(Expense expense) {
        return expenseRepository.save(expense);
    }
    
    // Business logic methods
    public List<Expense> findExpensesByCategory(String category) {
        return expenseRepository.findByCategory(category);
    }
    
    public List<Expense> findExpensesByDateRange(LocalDate startDate, LocalDate endDate) {
        return expenseRepository.findByDateBetween(startDate, endDate);
    }
    
    public List<Expense> findExpensesByCategoryAndDateRange(String category, LocalDate startDate, LocalDate endDate) {
        return expenseRepository.findByCategoryAndDateBetween(category, startDate, endDate);
    }
    
    public List<Expense> searchExpensesByDescription(String searchTerm) {
        return expenseRepository.findByDescriptionContainingIgnoreCase(searchTerm);
    }
    
    public List<String> getAllCategories() {
        return expenseRepository.findDistinctCategories();
    }
    
    public List<Expense> getCurrentMonthExpenses() {
        return expenseRepository.findCurrentMonthExpenses();
    }
    
    public List<Expense> getCurrentYearExpenses() {
        return expenseRepository.findCurrentYearExpenses();
    }
    
    public List<Expense> getRecentExpenses(int days) {
        LocalDate fromDate = LocalDate.now().minusDays(days);
        return expenseRepository.findRecentExpenses(fromDate);
    }
    
    public List<Expense> getTopExpenses() {
        return expenseRepository.findTop10ByOrderByAmountDesc();
    }
    
    // Summary and analytics methods
    public BigDecimal getTotalExpensesForDateRange(LocalDate startDate, LocalDate endDate) {
        BigDecimal total = expenseRepository.getTotalAmountByDateRange(startDate, endDate);
        return total != null ? total : BigDecimal.ZERO;
    }
    
    public BigDecimal getTotalExpensesForCurrentMonth() {
        YearMonth currentMonth = YearMonth.now();
        LocalDate startDate = currentMonth.atDay(1);
        LocalDate endDate = currentMonth.atEndOfMonth();
        return getTotalExpensesForDateRange(startDate, endDate);
    }
    
    public BigDecimal getTotalExpensesForCurrentYear() {
        LocalDate startDate = LocalDate.of(LocalDate.now().getYear(), 1, 1);
        LocalDate endDate = LocalDate.of(LocalDate.now().getYear(), 12, 31);
        return getTotalExpensesForDateRange(startDate, endDate);
    }
    
    public List<Object[]> getExpensesByCategory() {
        return expenseRepository.getTotalAmountByCategory();
    }
    
    // Validation methods
    public boolean isValidExpense(Expense expense) {
        return expense != null &&
               expense.getDescription() != null && !expense.getDescription().trim().isEmpty() &&
               expense.getAmount() != null && expense.getAmount().compareTo(BigDecimal.ZERO) > 0 &&
               expense.getDate() != null &&
               expense.getCategory() != null && !expense.getCategory().trim().isEmpty();
    }
    
    // Utility methods
    public long getTotalExpenseCount() {
        return expenseRepository.count();
    }
    
    public BigDecimal getAverageExpenseAmount() {
        List<Expense> allExpenses = expenseRepository.findAll();
        if (allExpenses.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal total = allExpenses.stream()
                .map(Expense::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        return total.divide(BigDecimal.valueOf(allExpenses.size()), 2, RoundingMode.HALF_UP);
    }
    
    public List<Expense> findExpensesAboveAmount(BigDecimal amount) {
        return expenseRepository.findByAmountGreaterThan(amount);
    }
}
