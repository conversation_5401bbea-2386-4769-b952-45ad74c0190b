package com.example.expense_backend.service;

import com.example.expense_backend.entity.Organization;
import com.example.expense_backend.entity.User;
import com.example.expense_backend.entity.UserRole;
import com.example.expense_backend.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class UserService {
    
    private final UserRepository userRepository;
    private final OrganizationService organizationService;
    private final PasswordEncoder passwordEncoder;
    
    @Autowired
    public UserService(UserRepository userRepository, 
                      OrganizationService organizationService,
                      PasswordEncoder passwordEncoder) {
        this.userRepository = userRepository;
        this.organizationService = organizationService;
        this.passwordEncoder = passwordEncoder;
    }
    
    // User creation and management
    public User createUser(User user, String password) {
        // Validate organization capacity
        if (!organizationService.canAddUser(user.getOrganization().getId())) {
            throw new IllegalStateException("Organization has reached maximum user limit");
        }
        
        // Validate uniqueness within organization
        validateUserUniqueness(user);
        
        // Encode password
        user.setPasswordHash(passwordEncoder.encode(password));
        
        // Set default role if not provided
        if (user.getRole() == null) {
            user.setRole(UserRole.EMPLOYEE);
        }
        
        return userRepository.save(user);
    }
    
    public Optional<User> findById(Long id) {
        return userRepository.findById(id);
    }
    
    public Optional<User> findByUsername(String username) {
        return userRepository.findByUsername(username);
    }
    
    public Optional<User> findByEmail(String email) {
        return userRepository.findByEmail(email);
    }
    
    public Optional<User> findByEmployeeId(String employeeId) {
        return userRepository.findByEmployeeId(employeeId);
    }
    
    public List<User> findByOrganization(Long organizationId) {
        return userRepository.findByOrganizationIdAndActiveTrue(organizationId);
    }
    
    public List<User> findByDepartment(Long departmentId) {
        return userRepository.findByDepartmentIdAndActiveTrue(departmentId);
    }
    
    public List<User> findByRole(UserRole role) {
        return userRepository.findByRoleAndActiveTrue(role);
    }
    
    public User updateUser(User user) {
        return userRepository.save(user);
    }
    
    public void deactivateUser(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("User not found: " + id));
        user.setActive(false);
        userRepository.save(user);
    }
    
    // Authentication and authorization
    public boolean authenticateUser(String username, String password) {
        Optional<User> userOpt = userRepository.findByUsername(username);
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            if (user.isActive() && passwordEncoder.matches(password, user.getPasswordHash())) {
                user.setLastLogin(LocalDateTime.now());
                userRepository.save(user);
                return true;
            }
        }
        return false;
    }
    
    public void changePassword(Long userId, String oldPassword, String newPassword) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("User not found: " + userId));
        
        if (!passwordEncoder.matches(oldPassword, user.getPasswordHash())) {
            throw new IllegalArgumentException("Current password is incorrect");
        }
        
        user.setPasswordHash(passwordEncoder.encode(newPassword));
        userRepository.save(user);
    }
    
    public void resetPassword(Long userId, String newPassword) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("User not found: " + userId));
        
        user.setPasswordHash(passwordEncoder.encode(newPassword));
        userRepository.save(user);
    }
    
    // Hierarchy and management
    public List<User> findDirectReports(Long managerId) {
        return userRepository.findDirectReports(managerId);
    }
    
    public List<User> findManagersByOrganization(Long organizationId) {
        return userRepository.findManagersByOrganization(organizationId);
    }
    
    public List<User> findApproversByOrganization(Long organizationId) {
        return userRepository.findApproversByOrganization(organizationId);
    }
    
    public void assignManager(Long userId, Long managerId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("User not found: " + userId));
        
        if (managerId != null) {
            User manager = userRepository.findById(managerId)
                    .orElseThrow(() -> new IllegalArgumentException("Manager not found: " + managerId));
            
            // Validate manager is in same organization
            if (!user.getOrganization().getId().equals(manager.getOrganization().getId())) {
                throw new IllegalArgumentException("Manager must be in the same organization");
            }
            
            // Validate manager has appropriate role
            if (!manager.isManager()) {
                throw new IllegalArgumentException("Assigned manager must have manager role");
            }
            
            user.setManager(manager);
        } else {
            user.setManager(null);
        }
        
        userRepository.save(user);
    }
    
    // Role management
    public void updateUserRole(Long userId, UserRole newRole) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("User not found: " + userId));
        
        UserRole oldRole = user.getRole();
        user.setRole(newRole);
        userRepository.save(user);
        
        // If demoting from manager role, reassign direct reports
        if (oldRole.canApproveExpenses() && !newRole.canApproveExpenses()) {
            reassignDirectReports(userId);
        }
    }
    
    private void reassignDirectReports(Long formerManagerId) {
        List<User> directReports = userRepository.findDirectReports(formerManagerId);
        for (User report : directReports) {
            // Find alternative manager in same department or organization
            User newManager = findAlternativeManager(report);
            report.setManager(newManager);
            userRepository.save(report);
        }
    }
    
    private User findAlternativeManager(User user) {
        // Try to find manager in same department first
        if (user.getDepartment() != null) {
            List<User> departmentManagers = userRepository.findManagersByOrganization(user.getOrganization().getId())
                    .stream()
                    .filter(m -> m.getDepartment() != null && 
                               m.getDepartment().getId().equals(user.getDepartment().getId()) &&
                               !m.getId().equals(user.getId()))
                    .toList();
            
            if (!departmentManagers.isEmpty()) {
                return departmentManagers.get(0);
            }
        }
        
        // Fall back to any manager in organization
        List<User> orgManagers = userRepository.findManagersByOrganization(user.getOrganization().getId())
                .stream()
                .filter(m -> !m.getId().equals(user.getId()))
                .toList();
        
        return orgManagers.isEmpty() ? null : orgManagers.get(0);
    }
    
    // Validation methods
    private void validateUserUniqueness(User user) {
        Long orgId = user.getOrganization().getId();
        
        if (userRepository.existsByUsernameAndOrganizationId(user.getUsername(), orgId)) {
            throw new IllegalArgumentException("Username already exists in organization: " + user.getUsername());
        }
        
        if (userRepository.existsByEmailAndOrganizationId(user.getEmail(), orgId)) {
            throw new IllegalArgumentException("Email already exists in organization: " + user.getEmail());
        }
        
        if (user.getEmployeeId() != null && 
            userRepository.existsByEmployeeIdAndOrganizationId(user.getEmployeeId(), orgId)) {
            throw new IllegalArgumentException("Employee ID already exists in organization: " + user.getEmployeeId());
        }
    }
    
    public boolean isValidUsername(String username) {
        return username != null && username.matches("^[a-zA-Z0-9._-]{3,50}$");
    }
    
    public boolean isValidEmail(String email) {
        return email != null && email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
    }
    
    public boolean isValidPassword(String password) {
        return password != null && password.length() >= 8 && 
               password.matches(".*[A-Z].*") && 
               password.matches(".*[a-z].*") && 
               password.matches(".*[0-9].*");
    }
}
