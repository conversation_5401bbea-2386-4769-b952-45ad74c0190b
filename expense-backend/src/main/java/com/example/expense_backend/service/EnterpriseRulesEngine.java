package com.example.expense_backend.service;

import com.example.expense_backend.entity.Expense;
import com.example.expense_backend.service.rules.RuleResult;
import org.kie.api.runtime.KieContainer;
import org.kie.api.runtime.KieSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Enterprise Business Rules Engine Service
 * 
 * This service demonstrates enterprise patterns for:
 * - Business rules separation from application logic
 * - Dynamic rule execution and evaluation
 * - Multi-domain rule processing (validation, approval, policy, fraud)
 * - Audit trail and decision tracking
 * - Performance monitoring and metrics
 * 
 * Learning Objectives:
 * - Understanding business rules engines in enterprise architecture
 * - Implementing rule-based decision making
 * - Managing complex business logic externally
 * - Creating auditable decision processes
 */
// Temporarily disabled due to Drools compilation issues
// @Service
public class EnterpriseRulesEngine {
    
    private static final Logger logger = LoggerFactory.getLogger(EnterpriseRulesEngine.class);
    
    @Autowired
    private KieContainer kieContainer;
    
    /**
     * Execute all business rules for an expense
     * 
     * This method demonstrates the enterprise pattern of:
     * - Centralized rule execution
     * - Multi-domain rule processing
     * - Comprehensive result aggregation
     */
    public RuleResult executeAllRules(Expense expense) {
        logger.info("Executing business rules for expense: {}", expense.getId());
        
        long startTime = System.currentTimeMillis();
        RuleResult result = new RuleResult();
        
        KieSession kieSession = kieContainer.newKieSession();
        try {
            // Set global variables
            kieSession.setGlobal("ruleResult", result);
            
            // Insert facts into working memory
            kieSession.insert(expense);
            if (expense.getUser() != null) {
                kieSession.insert(expense.getUser());
                if (expense.getUser().getDepartment() != null) {
                    kieSession.insert(expense.getUser().getDepartment());
                }
            }
            
            // Execute all rules
            int rulesExecuted = kieSession.fireAllRules();
            
            long executionTime = System.currentTimeMillis() - startTime;
            logger.info("Rules execution completed: {} rules fired in {}ms for expense {}", 
                       rulesExecuted, executionTime, expense.getId());
            
            // Log results summary
            logRuleResults(expense, result);

        } catch (Exception e) {
            logger.error("Error executing rules for expense {}: {}", expense.getId(), e.getMessage(), e);
            result.addValidationError("RULES_ENGINE_ERROR",
                                    "Error executing business rules: " + e.getMessage(),
                                    expense.getId());
        } finally {
            if (kieSession != null) {
                kieSession.dispose();
            }
        }
        
        return result;
    }
    
    /**
     * Execute validation rules only
     */
    public RuleResult executeValidationRules(Expense expense) {
        logger.info("Executing validation rules for expense: {}", expense.getId());

        RuleResult result = new RuleResult();
        KieSession kieSession = kieContainer.newKieSession();

        try {
            kieSession.setGlobal("ruleResult", result);
            kieSession.insert(expense);

            // Execute only validation rules (using agenda filters could be implemented)
            kieSession.fireAllRules();

            logger.info("Validation rules completed for expense {}: {} errors, {} warnings",
                       expense.getId(),
                       result.getValidationErrors().size(),
                       result.getValidationWarnings().size());

        } catch (Exception e) {
            logger.error("Error executing validation rules for expense {}: {}",
                        expense.getId(), e.getMessage(), e);
        } finally {
            if (kieSession != null) {
                kieSession.dispose();
            }
        }

        return result;
    }
    
    /**
     * Execute approval workflow rules
     */
    public RuleResult executeApprovalRules(Expense expense) {
        logger.info("Executing approval rules for expense: {}", expense.getId());

        RuleResult result = new RuleResult();
        KieSession kieSession = kieContainer.newKieSession();

        try {
            kieSession.setGlobal("ruleResult", result);
            kieSession.insert(expense);
            if (expense.getUser() != null) {
                kieSession.insert(expense.getUser());
                if (expense.getUser().getDepartment() != null) {
                    kieSession.insert(expense.getUser().getDepartment());
                }
            }

            kieSession.fireAllRules();

            logger.info("Approval rules completed for expense {}: {} requirements, {} decisions",
                       expense.getId(),
                       result.getApprovalRequirements().size(),
                       result.getApprovalDecisions().size());

        } catch (Exception e) {
            logger.error("Error executing approval rules for expense {}: {}",
                        expense.getId(), e.getMessage(), e);
        } finally {
            if (kieSession != null) {
                kieSession.dispose();
            }
        }

        return result;
    }
    
    /**
     * Execute fraud detection rules
     */
    public RuleResult executeFraudDetectionRules(List<Expense> expenses) {
        logger.info("Executing fraud detection rules for {} expenses", expenses.size());

        RuleResult result = new RuleResult();
        KieSession kieSession = kieContainer.newKieSession();

        try {
            kieSession.setGlobal("ruleResult", result);

            // Insert all expenses for pattern detection
            for (Expense expense : expenses) {
                kieSession.insert(expense);
                if (expense.getUser() != null) {
                    kieSession.insert(expense.getUser());
                }
            }

            kieSession.fireAllRules();

            logger.info("Fraud detection completed: {} alerts generated",
                       result.getFraudAlerts().size());

        } catch (Exception e) {
            logger.error("Error executing fraud detection rules: {}", e.getMessage(), e);
        } finally {
            if (kieSession != null) {
                kieSession.dispose();
            }
        }

        return result;
    }
    
    /**
     * Log comprehensive rule execution results
     */
    private void logRuleResults(Expense expense, RuleResult result) {
        if (result.hasErrors()) {
            logger.warn("Expense {} has validation errors: {}", 
                       expense.getId(), result.getValidationErrors());
        }
        
        if (result.hasWarnings()) {
            logger.info("Expense {} has validation warnings: {}", 
                       expense.getId(), result.getValidationWarnings());
        }
        
        if (result.requiresApproval()) {
            logger.info("Expense {} requires approval: {}", 
                       expense.getId(), result.getApprovalRequirements());
        }
        
        if (result.hasAutoApproval()) {
            logger.info("Expense {} auto-approved: {}", 
                       expense.getId(), result.getApprovalDecisions());
        }
        
        if (result.hasFraudAlerts()) {
            logger.warn("Expense {} triggered fraud alerts: {}", 
                       expense.getId(), result.getFraudAlerts());
        }
        
        if (!result.getPolicyChecks().isEmpty()) {
            logger.info("Expense {} policy checks: {}", 
                       expense.getId(), result.getPolicyChecks());
        }
    }
}
