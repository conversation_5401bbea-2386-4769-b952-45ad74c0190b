package com.example.expense_backend.service;

import com.example.expense_backend.entity.Organization;
import com.example.expense_backend.entity.SubscriptionPlan;
import com.example.expense_backend.repository.OrganizationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@Transactional
public class OrganizationService {
    
    private final OrganizationRepository organizationRepository;
    
    @Autowired
    public OrganizationService(OrganizationRepository organizationRepository) {
        this.organizationRepository = organizationRepository;
    }
    
    // Basic CRUD operations
    public Organization createOrganization(Organization organization) {
        // Generate unique tenant ID if not provided
        if (organization.getTenantId() == null || organization.getTenantId().isEmpty()) {
            organization.setTenantId(generateTenantId());
        }
        
        // Validate tenant ID uniqueness
        if (organizationRepository.existsByTenantId(organization.getTenantId())) {
            throw new IllegalArgumentException("Tenant ID already exists: " + organization.getTenantId());
        }
        
        // Validate domain uniqueness if provided
        if (organization.getDomain() != null && !organization.getDomain().isEmpty()) {
            if (organizationRepository.existsByDomain(organization.getDomain())) {
                throw new IllegalArgumentException("Domain already exists: " + organization.getDomain());
            }
        }
        
        // Set default subscription plan if not provided
        if (organization.getSubscriptionPlan() == null) {
            organization.setSubscriptionPlan(SubscriptionPlan.STARTER);
        }
        
        // Set max users based on subscription plan
        if (organization.getMaxUsers() == null) {
            organization.setMaxUsers(organization.getSubscriptionPlan().getMaxUsers());
        }
        
        return organizationRepository.save(organization);
    }
    
    public Optional<Organization> findById(Long id) {
        return organizationRepository.findById(id);
    }
    
    public Optional<Organization> findByTenantId(String tenantId) {
        return organizationRepository.findByTenantId(tenantId);
    }
    
    public Optional<Organization> findByDomain(String domain) {
        return organizationRepository.findByDomain(domain);
    }
    
    public List<Organization> findAllActive() {
        return organizationRepository.findByActiveTrue();
    }
    
    public Organization updateOrganization(Organization organization) {
        return organizationRepository.save(organization);
    }
    
    public void deleteOrganization(Long id) {
        Organization organization = organizationRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("Organization not found: " + id));
        organization.setActive(false);
        organizationRepository.save(organization);
    }
    
    // Business logic methods
    public boolean canAddUser(Long organizationId) {
        Organization organization = organizationRepository.findById(organizationId)
                .orElseThrow(() -> new IllegalArgumentException("Organization not found: " + organizationId));
        
        Long currentUserCount = organizationRepository.getActiveUserCount(organizationId);
        return currentUserCount < organization.getMaxUsers();
    }
    
    public void upgradeSubscription(Long organizationId, SubscriptionPlan newPlan) {
        Organization organization = organizationRepository.findById(organizationId)
                .orElseThrow(() -> new IllegalArgumentException("Organization not found: " + organizationId));
        
        organization.setSubscriptionPlan(newPlan);
        organization.setMaxUsers(newPlan.getMaxUsers());
        organizationRepository.save(organization);
    }
    
    // Statistics and reporting
    public OrganizationStats getOrganizationStats(Long organizationId) {
        Organization organization = organizationRepository.findById(organizationId)
                .orElseThrow(() -> new IllegalArgumentException("Organization not found: " + organizationId));
        
        Long activeUsers = organizationRepository.getActiveUserCount(organizationId);
        Long totalExpenses = organizationRepository.getTotalExpenseCount(organizationId);
        BigDecimal totalAmount = organizationRepository.getTotalApprovedExpenseAmount(organizationId);
        
        return new OrganizationStats(organization, activeUsers, totalExpenses, totalAmount);
    }
    
    // Validation methods
    public boolean isValidTenantId(String tenantId) {
        return tenantId != null && !tenantId.trim().isEmpty() && 
               tenantId.matches("^[a-zA-Z0-9-_]+$") && 
               tenantId.length() >= 3 && tenantId.length() <= 50;
    }
    
    public boolean isValidDomain(String domain) {
        if (domain == null || domain.trim().isEmpty()) return true; // Domain is optional
        return domain.matches("^[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");
    }
    
    // Utility methods
    private String generateTenantId() {
        return "org-" + UUID.randomUUID().toString().substring(0, 8);
    }
    
    // Inner class for organization statistics
    public static class OrganizationStats {
        private final Organization organization;
        private final Long activeUsers;
        private final Long totalExpenses;
        private final BigDecimal totalAmount;
        
        public OrganizationStats(Organization organization, Long activeUsers, Long totalExpenses, BigDecimal totalAmount) {
            this.organization = organization;
            this.activeUsers = activeUsers;
            this.totalExpenses = totalExpenses;
            this.totalAmount = totalAmount != null ? totalAmount : BigDecimal.ZERO;
        }
        
        // Getters
        public Organization getOrganization() { return organization; }
        public Long getActiveUsers() { return activeUsers; }
        public Long getTotalExpenses() { return totalExpenses; }
        public BigDecimal getTotalAmount() { return totalAmount; }
        
        public double getUserUtilization() {
            if (organization.getMaxUsers() == 0) return 0.0;
            return (activeUsers.doubleValue() / organization.getMaxUsers()) * 100.0;
        }
        
        public BigDecimal getAverageExpenseAmount() {
            if (totalExpenses == 0) return BigDecimal.ZERO;
            return totalAmount.divide(BigDecimal.valueOf(totalExpenses), 2, java.math.RoundingMode.HALF_UP);
        }
    }
}
