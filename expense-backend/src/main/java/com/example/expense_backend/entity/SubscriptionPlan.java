package com.example.expense_backend.entity;

public enum SubscriptionPlan {
    STARTER("Starter", 10, "Basic expense tracking"),
    PROFESSIONAL("Professional", 50, "Advanced features with approvals"),
    ENTERPRISE("Enterprise", 500, "Full enterprise features"),
    UNLIMITED("Unlimited", Integer.MAX_VALUE, "Unlimited users and features");
    
    private final String displayName;
    private final int maxUsers;
    private final String description;
    
    SubscriptionPlan(String displayName, int maxUsers, String description) {
        this.displayName = displayName;
        this.maxUsers = maxUsers;
        this.description = description;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public int getMaxUsers() {
        return maxUsers;
    }
    
    public String getDescription() {
        return description;
    }
}
