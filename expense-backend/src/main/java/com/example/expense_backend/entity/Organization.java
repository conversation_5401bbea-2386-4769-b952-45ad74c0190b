package com.example.expense_backend.entity;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.Set;

@Entity
@Table(name = "organizations")
public class Organization {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(unique = true, nullable = false)
    private String tenantId;
    
    @Column(nullable = false)
    private String name;
    
    @Column
    private String domain;
    
    @Column
    private String address;
    
    @Column
    private String contactEmail;
    
    @Column
    private String contactPhone;
    
    @Enumerated(EnumType.STRING)
    private SubscriptionPlan subscriptionPlan;
    
    @Column(name = "max_users")
    private Integer maxUsers;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @Column
    private boolean active = true;
    
    // Organization settings
    @Column(name = "default_currency")
    private String defaultCurrency = "USD";
    
    @Column(name = "expense_approval_required")
    private boolean expenseApprovalRequired = true;
    
    @Column(name = "receipt_required")
    private boolean receiptRequired = false;
    
    @Column(name = "auto_approve_limit")
    private java.math.BigDecimal autoApproveLimit;
    
    // Relationships
    @OneToMany(mappedBy = "organization", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<User> users;
    
    @OneToMany(mappedBy = "organization", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<Department> departments;
    
    @OneToMany(mappedBy = "organization", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<ExpensePolicy> expensePolicies;
    
    // Constructors
    public Organization() {
        this.createdAt = LocalDateTime.now();
    }
    
    public Organization(String tenantId, String name) {
        this();
        this.tenantId = tenantId;
        this.name = name;
    }
    
    // JPA lifecycle callbacks
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTenantId() {
        return tenantId;
    }
    
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDomain() {
        return domain;
    }
    
    public void setDomain(String domain) {
        this.domain = domain;
    }
    
    public String getAddress() {
        return address;
    }
    
    public void setAddress(String address) {
        this.address = address;
    }
    
    public String getContactEmail() {
        return contactEmail;
    }
    
    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }
    
    public String getContactPhone() {
        return contactPhone;
    }
    
    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }
    
    public SubscriptionPlan getSubscriptionPlan() {
        return subscriptionPlan;
    }
    
    public void setSubscriptionPlan(SubscriptionPlan subscriptionPlan) {
        this.subscriptionPlan = subscriptionPlan;
    }
    
    public Integer getMaxUsers() {
        return maxUsers;
    }
    
    public void setMaxUsers(Integer maxUsers) {
        this.maxUsers = maxUsers;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public boolean isActive() {
        return active;
    }
    
    public void setActive(boolean active) {
        this.active = active;
    }
    
    public String getDefaultCurrency() {
        return defaultCurrency;
    }
    
    public void setDefaultCurrency(String defaultCurrency) {
        this.defaultCurrency = defaultCurrency;
    }
    
    public boolean isExpenseApprovalRequired() {
        return expenseApprovalRequired;
    }
    
    public void setExpenseApprovalRequired(boolean expenseApprovalRequired) {
        this.expenseApprovalRequired = expenseApprovalRequired;
    }
    
    public boolean isReceiptRequired() {
        return receiptRequired;
    }
    
    public void setReceiptRequired(boolean receiptRequired) {
        this.receiptRequired = receiptRequired;
    }
    
    public java.math.BigDecimal getAutoApproveLimit() {
        return autoApproveLimit;
    }
    
    public void setAutoApproveLimit(java.math.BigDecimal autoApproveLimit) {
        this.autoApproveLimit = autoApproveLimit;
    }
    
    public Set<User> getUsers() {
        return users;
    }
    
    public void setUsers(Set<User> users) {
        this.users = users;
    }
    
    public Set<Department> getDepartments() {
        return departments;
    }
    
    public void setDepartments(Set<Department> departments) {
        this.departments = departments;
    }
    
    public Set<ExpensePolicy> getExpensePolicies() {
        return expensePolicies;
    }
    
    public void setExpensePolicies(Set<ExpensePolicy> expensePolicies) {
        this.expensePolicies = expensePolicies;
    }
    
    @Override
    public String toString() {
        return "Organization{" +
                "id=" + id +
                ", tenantId='" + tenantId + '\'' +
                ", name='" + name + '\'' +
                ", domain='" + domain + '\'' +
                ", subscriptionPlan=" + subscriptionPlan +
                ", active=" + active +
                '}';
    }
}
