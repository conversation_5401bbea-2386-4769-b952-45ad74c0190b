package com.example.expense_backend.entity;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Set;

@Entity
@Table(name = "departments")
public class Department {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String name;
    
    @Column(unique = true, nullable = false)
    private String code;
    
    @Column
    private String description;
    
    @Column(name = "budget_code")
    private String budgetCode;
    
    @Column(name = "cost_center")
    private String costCenter;
    
    @Column(name = "monthly_budget")
    private BigDecimal monthlyBudget;
    
    @Column(name = "annual_budget")
    private BigDecimal annualBudget;
    
    @Column
    private boolean active = true;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "organization_id", nullable = false)
    private Organization organization;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "department_head_id")
    private User departmentHead;
    
    @OneToMany(mappedBy = "department", fetch = FetchType.LAZY)
    private Set<User> employees;
    
    // Constructors
    public Department() {
        this.createdAt = LocalDateTime.now();
    }
    
    public Department(String name, String code, Organization organization) {
        this();
        this.name = name;
        this.code = code;
        this.organization = organization;
    }
    
    // JPA lifecycle callbacks
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getBudgetCode() {
        return budgetCode;
    }
    
    public void setBudgetCode(String budgetCode) {
        this.budgetCode = budgetCode;
    }
    
    public String getCostCenter() {
        return costCenter;
    }
    
    public void setCostCenter(String costCenter) {
        this.costCenter = costCenter;
    }
    
    public BigDecimal getMonthlyBudget() {
        return monthlyBudget;
    }
    
    public void setMonthlyBudget(BigDecimal monthlyBudget) {
        this.monthlyBudget = monthlyBudget;
    }
    
    public BigDecimal getAnnualBudget() {
        return annualBudget;
    }
    
    public void setAnnualBudget(BigDecimal annualBudget) {
        this.annualBudget = annualBudget;
    }
    
    public boolean isActive() {
        return active;
    }
    
    public void setActive(boolean active) {
        this.active = active;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public Organization getOrganization() {
        return organization;
    }
    
    public void setOrganization(Organization organization) {
        this.organization = organization;
    }
    
    public User getDepartmentHead() {
        return departmentHead;
    }
    
    public void setDepartmentHead(User departmentHead) {
        this.departmentHead = departmentHead;
    }
    
    public Set<User> getEmployees() {
        return employees;
    }
    
    public void setEmployees(Set<User> employees) {
        this.employees = employees;
    }
    
    @Override
    public String toString() {
        return "Department{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", code='" + code + '\'' +
                ", active=" + active +
                '}';
    }
}
