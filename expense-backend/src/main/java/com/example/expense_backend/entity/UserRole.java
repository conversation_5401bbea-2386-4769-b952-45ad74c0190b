package com.example.expense_backend.entity;

public enum UserRole {
    E<PERSON><PERSON><PERSON>YEE("Employee", "Can submit and manage own expenses"),
    MANAGER("Manager", "Can approve team expenses and manage own expenses"),
    FINANCE_ADMIN("Finance Admin", "Can manage all financial aspects and reports"),
    ADMIN("Administrator", "Full system access and user management"),
    AUDITOR("Auditor", "Read-only access for compliance and auditing");
    
    private final String displayName;
    private final String description;
    
    UserRole(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    public boolean canApproveExpenses() {
        return this == MANAGER || this == FINANCE_ADMIN || this == ADMIN;
    }
    
    public boolean canManageUsers() {
        return this == ADMIN;
    }
    
    public boolean canViewReports() {
        return this == MANAGER || this == FINANCE_ADMIN || this == ADMIN || this == AUDITOR;
    }
    
    public boolean canManageOrganization() {
        return this == ADMIN;
    }
}
