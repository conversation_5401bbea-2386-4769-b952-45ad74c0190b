package com.example.expense_backend.entity;

public enum ExpenseStatus {
    DRAFT("Draft", "Expense is being prepared"),
    SUBMITTED("Submitted", "Expense submitted for approval"),
    UNDER_REVIEW("Under Review", "Expense is being reviewed"),
    APPROVED("Approved", "Expense has been approved"),
    REJECTED("Rejected", "Expense has been rejected"),
    PAID("Paid", "Expense has been paid"),
    CANCELLED("Cancelled", "Expense has been cancelled");
    
    private final String displayName;
    private final String description;
    
    ExpenseStatus(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    public boolean isEditable() {
        return this == DRAFT || this == REJECTED;
    }
    
    public boolean isPending() {
        return this == SUBMITTED || this == UNDER_REVIEW;
    }
    
    public boolean isFinal() {
        return this == APPROVED || this == PAID || this == CANCELLED;
    }
    
    public boolean canBeApproved() {
        return this == SUBMITTED || this == UNDER_REVIEW;
    }
    
    public boolean canBeRejected() {
        return this == SUBMITTED || this == UNDER_REVIEW;
    }
}
