package com.example.expense_backend.entity;

public enum ApprovalStatus {
    PENDING("Pending", "Waiting for approval"),
    APPROVED("Approved", "Approved by approver"),
    REJECTED("Rejected", "Rejected by approver"),
    DELEGATED("Delegated", "Delegated to another approver"),
    ESCALATED("Escalated", "Escalated due to timeout");
    
    private final String displayName;
    private final String description;
    
    ApprovalStatus(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    public boolean isFinal() {
        return this == APPROVED || this == REJECTED;
    }
    
    public boolean isPending() {
        return this == PENDING || this == DELEGATED || this == ESCALATED;
    }
}
