package com.example.expense_backend.entity;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "expense_policies")
public class ExpensePolicy {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String name;
    
    @Column
    private String description;
    
    @Column(nullable = false)
    private String category;
    
    @Column(name = "max_amount")
    private BigDecimal maxAmount;
    
    @Column(name = "requires_receipt")
    private boolean requiresReceipt = false;
    
    @Column(name = "receipt_required_above")
    private BigDecimal receiptRequiredAbove;
    
    @Column(name = "requires_approval")
    private boolean requiresApproval = true;
    
    @Column(name = "approval_required_above")
    private BigDecimal approvalRequiredAbove;
    
    @Column(name = "auto_approve_below")
    private BigDecimal autoApproveBelow;
    
    @Column(name = "business_purpose_required")
    private boolean businessPurposeRequired = true;
    
    @Column(name = "project_code_required")
    private boolean projectCodeRequired = false;
    
    @Column
    private boolean active = true;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "organization_id", nullable = false)
    private Organization organization;
    
    // Constructors
    public ExpensePolicy() {
        this.createdAt = LocalDateTime.now();
    }
    
    public ExpensePolicy(String name, String category, Organization organization) {
        this();
        this.name = name;
        this.category = category;
        this.organization = organization;
    }
    
    // JPA lifecycle callbacks
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
    
    // Business logic methods
    public boolean isReceiptRequired(BigDecimal amount) {
        if (requiresReceipt) return true;
        if (receiptRequiredAbove != null) {
            return amount.compareTo(receiptRequiredAbove) >= 0;
        }
        return false;
    }
    
    public boolean isApprovalRequired(BigDecimal amount) {
        if (!requiresApproval) return false;
        if (autoApproveBelow != null && amount.compareTo(autoApproveBelow) < 0) {
            return false;
        }
        if (approvalRequiredAbove != null) {
            return amount.compareTo(approvalRequiredAbove) >= 0;
        }
        return true;
    }
    
    public boolean isAmountAllowed(BigDecimal amount) {
        if (maxAmount == null) return true;
        return amount.compareTo(maxAmount) <= 0;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public BigDecimal getMaxAmount() {
        return maxAmount;
    }
    
    public void setMaxAmount(BigDecimal maxAmount) {
        this.maxAmount = maxAmount;
    }
    
    public boolean isRequiresReceipt() {
        return requiresReceipt;
    }
    
    public void setRequiresReceipt(boolean requiresReceipt) {
        this.requiresReceipt = requiresReceipt;
    }
    
    public BigDecimal getReceiptRequiredAbove() {
        return receiptRequiredAbove;
    }
    
    public void setReceiptRequiredAbove(BigDecimal receiptRequiredAbove) {
        this.receiptRequiredAbove = receiptRequiredAbove;
    }
    
    public boolean isRequiresApproval() {
        return requiresApproval;
    }
    
    public void setRequiresApproval(boolean requiresApproval) {
        this.requiresApproval = requiresApproval;
    }
    
    public BigDecimal getApprovalRequiredAbove() {
        return approvalRequiredAbove;
    }
    
    public void setApprovalRequiredAbove(BigDecimal approvalRequiredAbove) {
        this.approvalRequiredAbove = approvalRequiredAbove;
    }
    
    public BigDecimal getAutoApproveBelow() {
        return autoApproveBelow;
    }
    
    public void setAutoApproveBelow(BigDecimal autoApproveBelow) {
        this.autoApproveBelow = autoApproveBelow;
    }
    
    public boolean isBusinessPurposeRequired() {
        return businessPurposeRequired;
    }
    
    public void setBusinessPurposeRequired(boolean businessPurposeRequired) {
        this.businessPurposeRequired = businessPurposeRequired;
    }
    
    public boolean isProjectCodeRequired() {
        return projectCodeRequired;
    }
    
    public void setProjectCodeRequired(boolean projectCodeRequired) {
        this.projectCodeRequired = projectCodeRequired;
    }
    
    public boolean isActive() {
        return active;
    }
    
    public void setActive(boolean active) {
        this.active = active;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public Organization getOrganization() {
        return organization;
    }
    
    public void setOrganization(Organization organization) {
        this.organization = organization;
    }
    
    @Override
    public String toString() {
        return "ExpensePolicy{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", category='" + category + '\'' +
                ", maxAmount=" + maxAmount +
                ", requiresApproval=" + requiresApproval +
                ", active=" + active +
                '}';
    }
}
