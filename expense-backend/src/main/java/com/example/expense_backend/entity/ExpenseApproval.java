package com.example.expense_backend.entity;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "expense_approvals")
public class ExpenseApproval {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "approval_level", nullable = false)
    private Integer approvalLevel;
    
    @Enumerated(EnumType.STRING)
    private ApprovalStatus status = ApprovalStatus.PENDING;
    
    @Column
    private String comments;
    
    @Column(name = "approved_at")
    private LocalDateTime approvedAt;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "expense_id", nullable = false)
    private Expense expense;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "approver_id", nullable = false)
    private User approver;
    
    // Constructors
    public ExpenseApproval() {
        this.createdAt = LocalDateTime.now();
    }
    
    public ExpenseApproval(Expense expense, User approver, Integer approvalLevel) {
        this();
        this.expense = expense;
        this.approver = approver;
        this.approvalLevel = approvalLevel;
    }
    
    // Utility methods
    public void approve(String comments) {
        this.status = ApprovalStatus.APPROVED;
        this.comments = comments;
        this.approvedAt = LocalDateTime.now();
    }
    
    public void reject(String comments) {
        this.status = ApprovalStatus.REJECTED;
        this.comments = comments;
        this.approvedAt = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Integer getApprovalLevel() {
        return approvalLevel;
    }
    
    public void setApprovalLevel(Integer approvalLevel) {
        this.approvalLevel = approvalLevel;
    }
    
    public ApprovalStatus getStatus() {
        return status;
    }
    
    public void setStatus(ApprovalStatus status) {
        this.status = status;
    }
    
    public String getComments() {
        return comments;
    }
    
    public void setComments(String comments) {
        this.comments = comments;
    }
    
    public LocalDateTime getApprovedAt() {
        return approvedAt;
    }
    
    public void setApprovedAt(LocalDateTime approvedAt) {
        this.approvedAt = approvedAt;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public Expense getExpense() {
        return expense;
    }
    
    public void setExpense(Expense expense) {
        this.expense = expense;
    }
    
    public User getApprover() {
        return approver;
    }
    
    public void setApprover(User approver) {
        this.approver = approver;
    }
    
    @Override
    public String toString() {
        return "ExpenseApproval{" +
                "id=" + id +
                ", approvalLevel=" + approvalLevel +
                ", status=" + status +
                ", approvedAt=" + approvedAt +
                '}';
    }
}
