package com.example.expense_backend.config;

import org.kie.api.KieServices;
import org.kie.api.builder.KieBuilder;
import org.kie.api.builder.KieFileSystem;
import org.kie.api.builder.KieModule;
import org.kie.api.runtime.KieContainer;
import org.kie.api.runtime.KieSession;
import org.kie.internal.io.ResourceFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Enterprise Drools Configuration
 * 
 * This configuration sets up the Drools Business Rules Engine for:
 * - Expense validation rules
 * - Approval workflow rules  
 * - Policy enforcement rules
 * - Fraud detection rules
 * 
 * Learning Objectives:
 * - Understanding business rules engines
 * - Separating business logic from application code
 * - Dynamic rule management
 * - Enterprise decision management
 */
// Temporarily disabled due to compilation issues
// @Configuration
public class DroolsConfig {

    private static final String RULES_PATH = "rules/";
    
    @Bean
    public KieContainer kieContainer() {
        KieServices kieServices = KieServices.Factory.get();
        KieFileSystem kieFileSystem = kieServices.newKieFileSystem();
        
        // Load all rule files
        kieFileSystem.write(ResourceFactory.newClassPathResource(RULES_PATH + "expense-validation.drl"));
        kieFileSystem.write(ResourceFactory.newClassPathResource(RULES_PATH + "approval-workflow.drl"));
        kieFileSystem.write(ResourceFactory.newClassPathResource(RULES_PATH + "policy-enforcement.drl"));
        kieFileSystem.write(ResourceFactory.newClassPathResource(RULES_PATH + "fraud-detection.drl"));
        
        KieBuilder kieBuilder = kieServices.newKieBuilder(kieFileSystem);
        kieBuilder.buildAll();
        
        if (kieBuilder.getResults().hasMessages(org.kie.api.builder.Message.Level.ERROR)) {
            throw new RuntimeException("Build Errors:\n" + kieBuilder.getResults().toString());
        }
        
        KieModule kieModule = kieBuilder.getKieModule();
        return kieServices.newKieContainer(kieModule.getReleaseId());
    }
    
    @Bean
    public KieSession kieSession() {
        return kieContainer().newKieSession();
    }
}
