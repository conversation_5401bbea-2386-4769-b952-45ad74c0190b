package com.example.expense_backend;

import com.example.expense_backend.gui.ExpenseMainController;
import javafx.application.Application;
import javafx.application.Platform;
import javafx.fxml.FXMLLoader;
import javafx.scene.Scene;
import javafx.stage.Stage;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ConfigurableApplicationContext;

public class ExpenseGuiApplication extends Application {
    
    private ConfigurableApplicationContext springContext;
    
    @Override
    public void init() throws Exception {
        // Initialize Spring Boot context
        springContext = SpringApplication.run(ExpenseBackendApplication.class);
    }
    
    @Override
    public void start(Stage primaryStage) throws Exception {
        try {
            // Load main application directly for now (we'll add login later)
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/expense-main.fxml"));

            // Set the controller factory to use Spring's bean factory
            loader.setControllerFactory(springContext::getBean);

            Scene scene = new Scene(loader.load(), 1400, 900);

            // Get the main controller and set a default user for testing
            ExpenseMainController mainController = loader.getController();

            // For now, set the admin user as default
            try {
                com.example.expense_backend.service.UserService userService = springContext.getBean(com.example.expense_backend.service.UserService.class);
                java.util.Optional<com.example.expense_backend.entity.User> adminUser = userService.findByUsername("admin");
                if (adminUser.isPresent()) {
                    mainController.setCurrentUser(adminUser.get());
                }
            } catch (Exception e) {
                System.err.println("Could not set default user: " + e.getMessage());
            }

            // Set up the primary stage
            primaryStage.setTitle("Expense Management System - Enterprise Edition");
            primaryStage.setScene(scene);
            primaryStage.setMinWidth(1000);
            primaryStage.setMinHeight(700);

            // Handle window close event
            primaryStage.setOnCloseRequest(event -> {
                Platform.exit();
                System.exit(0);
            });

            primaryStage.show();

        } catch (Exception e) {
            e.printStackTrace();
            Platform.exit();
            System.exit(1);
        }
    }
    
    @Override
    public void stop() throws Exception {
        // Close Spring context when JavaFX application stops
        if (springContext != null) {
            springContext.close();
        }
    }
    
    public static void main(String[] args) {
        // Set system property to prevent JavaFX from shutting down when last window is closed
        System.setProperty("javafx.application.Thread.currentThread().setContextClassLoader", "false");
        
        // Launch JavaFX application
        launch(args);
    }
}
