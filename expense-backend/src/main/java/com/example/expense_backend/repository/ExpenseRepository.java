package com.example.expense_backend.repository;

import com.example.expense_backend.entity.Expense;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Repository
public interface ExpenseRepository extends JpaRepository<Expense, Long> {
    
    // Find expenses by category
    List<Expense> findByCategory(String category);
    
    // Find expenses by date range
    List<Expense> findByDateBetween(LocalDate startDate, LocalDate endDate);
    
    // Find expenses by category and date range
    List<Expense> findByCategoryAndDateBetween(String category, LocalDate startDate, LocalDate endDate);
    
    // Find expenses greater than a certain amount
    List<Expense> findByAmountGreaterThan(BigDecimal amount);
    
    // Find expenses by description containing text (case insensitive)
    List<Expense> findByDescriptionContainingIgnoreCase(String description);
    
    // Get all distinct categories
    @Query("SELECT DISTINCT e.category FROM Expense e ORDER BY e.category")
    List<String> findDistinctCategories();
    
    // Get total amount by category
    @Query("SELECT e.category, SUM(e.amount) FROM Expense e GROUP BY e.category")
    List<Object[]> getTotalAmountByCategory();
    
    // Get total amount for a date range
    @Query("SELECT SUM(e.amount) FROM Expense e WHERE e.date BETWEEN :startDate AND :endDate")
    BigDecimal getTotalAmountByDateRange(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
    
    // Get expenses for current month
    @Query("SELECT e FROM Expense e WHERE YEAR(e.date) = YEAR(CURRENT_DATE) AND MONTH(e.date) = MONTH(CURRENT_DATE) ORDER BY e.date DESC")
    List<Expense> findCurrentMonthExpenses();
    
    // Get expenses for current year
    @Query("SELECT e FROM Expense e WHERE YEAR(e.date) = YEAR(CURRENT_DATE) ORDER BY e.date DESC")
    List<Expense> findCurrentYearExpenses();
    
    // Find top N expenses by amount
    List<Expense> findTop10ByOrderByAmountDesc();
    
    // Find recent expenses (last N days)
    @Query("SELECT e FROM Expense e WHERE e.date >= :date ORDER BY e.date DESC")
    List<Expense> findRecentExpenses(@Param("date") LocalDate date);
}
