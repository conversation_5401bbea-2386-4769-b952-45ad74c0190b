package com.example.expense_backend.repository;

import com.example.expense_backend.entity.User;
import com.example.expense_backend.entity.UserRole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    
    // Find user by username
    Optional<User> findByUsername(String username);
    
    // Find user by email
    Optional<User> findByEmail(String email);
    
    // Find user by employee ID
    Optional<User> findByEmployeeId(String employeeId);
    
    // Find users by organization
    List<User> findByOrganizationIdAndActiveTrue(Long organizationId);
    
    // Find users by department
    List<User> findByDepartmentIdAndActiveTrue(Long departmentId);
    
    // Find users by role
    List<User> findByRoleAndActiveTrue(UserRole role);
    
    // Find users by manager
    List<User> findByManagerIdAndActiveTrue(Long managerId);
    
    // Find managers in organization
    @Query("SELECT u FROM User u WHERE u.organization.id = :organizationId AND u.role IN ('MANAGER', 'ADMIN') AND u.active = true")
    List<User> findManagersByOrganization(@Param("organizationId") Long organizationId);
    
    // Find users who can approve expenses
    @Query("SELECT u FROM User u WHERE u.organization.id = :organizationId AND u.role IN ('MANAGER', 'ADMIN', 'FINANCE_ADMIN') AND u.active = true")
    List<User> findApproversByOrganization(@Param("organizationId") Long organizationId);
    
    // Check if username exists in organization
    boolean existsByUsernameAndOrganizationId(String username, Long organizationId);
    
    // Check if email exists in organization
    boolean existsByEmailAndOrganizationId(String email, Long organizationId);
    
    // Check if employee ID exists in organization
    boolean existsByEmployeeIdAndOrganizationId(String employeeId, Long organizationId);
    
    // Find direct reports
    @Query("SELECT u FROM User u WHERE u.manager.id = :managerId AND u.active = true")
    List<User> findDirectReports(@Param("managerId") Long managerId);
    
    // Find all subordinates (recursive)
    @Query(value = "WITH RECURSIVE subordinates AS (" +
           "SELECT id, username, first_name, last_name, manager_id, 1 as level " +
           "FROM users WHERE manager_id = :managerId AND active = true " +
           "UNION ALL " +
           "SELECT u.id, u.username, u.first_name, u.last_name, u.manager_id, s.level + 1 " +
           "FROM users u INNER JOIN subordinates s ON u.manager_id = s.id " +
           "WHERE u.active = true AND s.level < 10) " +
           "SELECT * FROM subordinates", nativeQuery = true)
    List<Object[]> findAllSubordinates(@Param("managerId") Long managerId);
}
