package com.example.expense_backend.repository;

import com.example.expense_backend.entity.Department;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Repository
public interface DepartmentRepository extends JpaRepository<Department, Long> {
    
    // Find department by code
    Optional<Department> findByCode(String code);
    
    // Find departments by organization
    List<Department> findByOrganizationIdAndActiveTrue(Long organizationId);
    
    // Find department by name in organization
    Optional<Department> findByNameAndOrganizationId(String name, Long organizationId);
    
    // Find department by code in organization
    Optional<Department> findByCodeAndOrganizationId(String code, Long organizationId);
    
    // Check if code exists in organization
    boolean existsByCodeAndOrganizationId(String code, Long organizationId);
    
    // Check if name exists in organization
    boolean existsByNameAndOrganizationId(String name, Long organizationId);
    
    // Get department statistics
    @Query("SELECT COUNT(u) FROM User u WHERE u.department.id = :departmentId AND u.active = true")
    Long getEmployeeCount(@Param("departmentId") Long departmentId);
    
    @Query("SELECT SUM(e.amount) FROM Expense e WHERE e.user.department.id = :departmentId AND e.status = 'APPROVED'")
    BigDecimal getTotalApprovedExpenses(@Param("departmentId") Long departmentId);
    
    @Query("SELECT SUM(e.amount) FROM Expense e WHERE e.user.department.id = :departmentId AND e.status = 'APPROVED' AND YEAR(e.date) = YEAR(CURRENT_DATE) AND MONTH(e.date) = MONTH(CURRENT_DATE)")
    BigDecimal getCurrentMonthExpenses(@Param("departmentId") Long departmentId);
    
    @Query("SELECT SUM(e.amount) FROM Expense e WHERE e.user.department.id = :departmentId AND e.status = 'APPROVED' AND YEAR(e.date) = YEAR(CURRENT_DATE)")
    BigDecimal getCurrentYearExpenses(@Param("departmentId") Long departmentId);
    
    // Find departments with budget overruns
    @Query("SELECT d FROM Department d WHERE d.organization.id = :organizationId AND d.monthlyBudget IS NOT NULL AND " +
           "(SELECT COALESCE(SUM(e.amount), 0) FROM Expense e WHERE e.user.department.id = d.id AND e.status = 'APPROVED' " +
           "AND YEAR(e.date) = YEAR(CURRENT_DATE) AND MONTH(e.date) = MONTH(CURRENT_DATE)) > d.monthlyBudget")
    List<Department> findDepartmentsOverBudget(@Param("organizationId") Long organizationId);
}
