package com.example.expense_backend.repository;

import com.example.expense_backend.entity.Organization;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface OrganizationRepository extends JpaRepository<Organization, Long> {
    
    // Find organization by tenant ID
    Optional<Organization> findByTenantId(String tenantId);
    
    // Find organization by domain
    Optional<Organization> findByDomain(String domain);
    
    // Find active organizations
    List<Organization> findByActiveTrue();
    
    // Find organizations by subscription plan
    List<Organization> findBySubscriptionPlan(com.example.expense_backend.entity.SubscriptionPlan subscriptionPlan);
    
    // Check if tenant ID exists
    boolean existsByTenantId(String tenantId);
    
    // Check if domain exists
    boolean existsByDomain(String domain);
    
    // Get organization statistics
    @Query("SELECT COUNT(u) FROM User u WHERE u.organization.id = :organizationId AND u.active = true")
    Long getActiveUserCount(@Param("organizationId") Long organizationId);
    
    @Query("SELECT COUNT(e) FROM Expense e WHERE e.user.organization.id = :organizationId")
    Long getTotalExpenseCount(@Param("organizationId") Long organizationId);
    
    @Query("SELECT SUM(e.amount) FROM Expense e WHERE e.user.organization.id = :organizationId AND e.status = 'APPROVED'")
    java.math.BigDecimal getTotalApprovedExpenseAmount(@Param("organizationId") Long organizationId);
}
