package com.example.expense_backend;

import com.example.expense_backend.entity.Expense;
import com.example.expense_backend.entity.User;
import com.example.expense_backend.entity.Department;
import com.example.expense_backend.entity.Organization;
import com.example.expense_backend.entity.UserRole;
import com.example.expense_backend.service.EnterpriseRulesEngine;
import com.example.expense_backend.service.rules.RuleResult;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class to demonstrate Drools business rules in action
 */
@SpringBootTest
public class DroolsRulesTest {

    @Autowired
    private EnterpriseRulesEngine rulesEngine;

    @Test
    public void testBusinessPurposeValidationRule() {
        // Create test data
        Organization org = new Organization();
        org.setName("Test Corp");
        
        Department dept = new Department();
        dept.setName("IT");
        dept.setCode("IT");
        dept.setOrganization(org);
        
        User user = new User();
        user.setUsername("testuser");
        user.setEmail("<EMAIL>");
        user.setRole(UserRole.EMPLOYEE);
        user.setDepartment(dept);
        
        // Test expense over $100 without proper business purpose
        Expense expense = new Expense();
        expense.setAmount(new BigDecimal("150.00"));
        expense.setDescription("Software purchase");
        expense.setCategory("Software");
        expense.setDate(LocalDate.now());
        expense.setUser(user);
        expense.setBusinessPurpose("Short"); // Too short - should trigger rule
        
        // Execute rules
        RuleResult result = rulesEngine.executeAllRules(expense);
        
        // Verify rule fired
        assertFalse(result.getValidationErrors().isEmpty(), 
                   "Should have validation error for insufficient business purpose");
        assertTrue(result.getValidationErrors().stream()
                  .anyMatch(error -> error.getCode().equals("BUSINESS_PURPOSE_REQUIRED")),
                  "Should have BUSINESS_PURPOSE_REQUIRED error");
        
        System.out.println("✓ Business Purpose Validation Rule Test Passed");
        System.out.println("  Errors: " + result.getValidationErrors());
    }

    @Test
    public void testAlcoholPurchaseBlockingRule() {
        // Create test data
        Organization org = new Organization();
        org.setName("Test Corp");
        
        Department dept = new Department();
        dept.setName("Sales");
        dept.setCode("SALES");
        dept.setOrganization(org);
        
        User user = new User();
        user.setUsername("salesuser");
        user.setEmail("<EMAIL>");
        user.setRole(UserRole.EMPLOYEE);
        user.setDepartment(dept);
        
        // Test expense with alcohol in description
        Expense expense = new Expense();
        expense.setAmount(new BigDecimal("75.00"));
        expense.setDescription("Client dinner with wine");
        expense.setCategory("Meals");
        expense.setDate(LocalDate.now());
        expense.setUser(user);
        expense.setBusinessPurpose("Client entertainment dinner meeting");
        
        // Execute rules
        RuleResult result = rulesEngine.executeAllRules(expense);
        
        // Verify rule fired
        assertFalse(result.getValidationErrors().isEmpty(), 
                   "Should have validation error for alcohol purchase");
        assertTrue(result.getValidationErrors().stream()
                  .anyMatch(error -> error.getCode().equals("ALCOHOL_NOT_PERMITTED")),
                  "Should have ALCOHOL_NOT_PERMITTED error");
        
        System.out.println("✓ Alcohol Purchase Blocking Rule Test Passed");
        System.out.println("  Errors: " + result.getValidationErrors());
    }

    @Test
    public void testMealReceiptWarningRule() {
        // Create test data
        Organization org = new Organization();
        org.setName("Test Corp");
        
        Department dept = new Department();
        dept.setName("Marketing");
        dept.setCode("MKT");
        dept.setOrganization(org);
        
        User user = new User();
        user.setUsername("mktuser");
        user.setEmail("<EMAIL>");
        user.setRole(UserRole.EMPLOYEE);
        user.setDepartment(dept);
        
        // Test meal expense over $25
        Expense expense = new Expense();
        expense.setAmount(new BigDecimal("35.00"));
        expense.setDescription("Business lunch");
        expense.setCategory("Meals");
        expense.setDate(LocalDate.now());
        expense.setUser(user);
        expense.setBusinessPurpose("Client meeting lunch discussion");
        
        // Execute rules
        RuleResult result = rulesEngine.executeAllRules(expense);
        
        // Verify warning rule fired
        assertFalse(result.getValidationWarnings().isEmpty(), 
                   "Should have validation warning for meal receipt");
        assertTrue(result.getValidationWarnings().stream()
                  .anyMatch(warning -> warning.getCode().equals("RECEIPT_RECOMMENDED")),
                  "Should have RECEIPT_RECOMMENDED warning");
        
        System.out.println("✓ Meal Receipt Warning Rule Test Passed");
        System.out.println("  Warnings: " + result.getValidationWarnings());
    }

    @Test
    public void testLargeExpenseAlertRule() {
        // Create test data
        Organization org = new Organization();
        org.setName("Test Corp");
        
        Department dept = new Department();
        dept.setName("Finance");
        dept.setCode("FIN");
        dept.setOrganization(org);
        
        User user = new User();
        user.setUsername("finuser");
        user.setEmail("<EMAIL>");
        user.setRole(UserRole.MANAGER);
        user.setDepartment(dept);
        
        // Test large expense over $5000
        Expense expense = new Expense();
        expense.setAmount(new BigDecimal("7500.00"));
        expense.setDescription("Enterprise software license");
        expense.setCategory("Software");
        expense.setDate(LocalDate.now());
        expense.setUser(user);
        expense.setBusinessPurpose("Annual enterprise software licensing for company-wide deployment");
        
        // Execute rules
        RuleResult result = rulesEngine.executeAllRules(expense);
        
        // Verify warning rule fired
        assertFalse(result.getValidationWarnings().isEmpty(), 
                   "Should have validation warning for large expense");
        assertTrue(result.getValidationWarnings().stream()
                  .anyMatch(warning -> warning.getCode().equals("LARGE_EXPENSE_ALERT")),
                  "Should have LARGE_EXPENSE_ALERT warning");
        
        System.out.println("✓ Large Expense Alert Rule Test Passed");
        System.out.println("  Warnings: " + result.getValidationWarnings());
    }

    @Test
    public void testValidExpensePassesRules() {
        // Create test data
        Organization org = new Organization();
        org.setName("Test Corp");
        
        Department dept = new Department();
        dept.setName("Engineering");
        dept.setCode("ENG");
        dept.setOrganization(org);
        
        User user = new User();
        user.setUsername("engineer");
        user.setEmail("<EMAIL>");
        user.setRole(UserRole.EMPLOYEE);
        user.setDepartment(dept);
        
        // Test valid expense that should pass all rules
        Expense expense = new Expense();
        expense.setAmount(new BigDecimal("45.00"));
        expense.setDescription("Office supplies");
        expense.setCategory("Office");
        expense.setDate(LocalDate.now());
        expense.setUser(user);
        expense.setBusinessPurpose("Monthly office supplies for team productivity");
        
        // Execute rules
        RuleResult result = rulesEngine.executeAllRules(expense);
        
        // Verify no errors (warnings might be present)
        assertTrue(result.getValidationErrors().isEmpty(), 
                  "Valid expense should not have validation errors");
        
        System.out.println("✓ Valid Expense Test Passed");
        System.out.println("  Errors: " + result.getValidationErrors().size());
        System.out.println("  Warnings: " + result.getValidationWarnings().size());
    }
}
