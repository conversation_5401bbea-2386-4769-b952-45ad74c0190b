#!/bin/bash

# Expense Tracker Launch Script
# This script launches the JavaFX Expense Tracker application

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Change to the application directory
cd "$SCRIPT_DIR"

# Check if Java is available
if ! command -v java &> /dev/null; then
    echo "Error: Java is not installed or not in PATH"
    echo "Please install Java 21 or later to run this application"
    exit 1
fi

# Check Java version
JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
if [ "$JAVA_VERSION" -lt 21 ]; then
    echo "Error: Java 21 or later is required"
    echo "Current Java version: $JAVA_VERSION"
    exit 1
fi

# Check if Maven wrapper exists
if [ ! -f "./mvnw" ]; then
    echo "Error: Maven wrapper not found"
    echo "Please ensure you're running this script from the expense-backend directory"
    exit 1
fi

# Make sure Maven wrapper is executable
chmod +x ./mvnw

echo "Starting Expense Tracker..."
echo "Application directory: $SCRIPT_DIR"

# Launch the application
./mvnw javafx:run

# Check if the application started successfully
if [ $? -ne 0 ]; then
    echo "Error: Failed to start Expense Tracker"
    echo "Please check the error messages above"
    read -p "Press Enter to close this window..."
    exit 1
fi
