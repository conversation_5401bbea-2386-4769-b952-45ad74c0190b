2025-07-12 20:24:06.360247+01:00 database: flush
org.h2.message.DbException: General error: "org.h2.mvstore.MVStoreException: The file is locked: /home/<USER>/Documents/expenses/expense-backend/data/expenses.mv.db [2.3.232/7]" [50000-232]
	at org.h2.message.DbException.get(DbException.java:212)
	at org.h2.message.DbException.convert(DbException.java:407)
	at org.h2.mvstore.db.Store.lambda$new$0(Store.java:122)
	at org.h2.mvstore.MVStore.handleException(MVStore.java:1546)
	at org.h2.mvstore.MVStore.panic(MVStore.java:371)
	at org.h2.mvstore.MVStore.<init>(MVStore.java:291)
	at org.h2.mvstore.MVStore$Builder.open(MVStore.java:2035)
	at org.h2.mvstore.db.Store.<init>(Store.java:133)
	at org.h2.engine.Database.<init>(Database.java:326)
	at org.h2.engine.Engine.openSession(Engine.java:92)
	at org.h2.engine.Engine.openSession(Engine.java:222)
	at org.h2.engine.Engine.createSession(Engine.java:201)
	at org.h2.engine.SessionRemote.connectEmbeddedOrServer(SessionRemote.java:344)
	at org.h2.jdbc.JdbcConnection.<init>(JdbcConnection.java:124)
	at org.h2.Driver.connect(Driver.java:59)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:139)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:368)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:205)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:483)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:571)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:101)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:111)
	at org.hibernate.engine.jdbc.connections.internal.DatasourceConnectionProviderImpl.getConnection(DatasourceConnectionProviderImpl.java:126)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess.obtainConnection(JdbcEnvironmentInitiator.java:483)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcIsolationDelegate.delegateWork(JdbcIsolationDelegate.java:61)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.getJdbcEnvironmentUsingJdbcMetadata(JdbcEnvironmentInitiator.java:336)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:129)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:81)
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:130)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:238)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:215)
	at org.hibernate.boot.model.relational.Database.<init>(Database.java:45)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.getDatabase(InFlightMetadataCollectorImpl.java:226)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:194)
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:171)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1442)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1513)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:66)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:390)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:419)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:400)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:366)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1873)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1822)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:207)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:970)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.example.expense_backend.ExpenseGuiApplication.init(ExpenseGuiApplication.java:19)
	at javafx.graphics@21.0.2/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:817)
	at javafx.graphics@21.0.2/com.sun.javafx.application.LauncherImpl.lambda$launchApplication$2(LauncherImpl.java:196)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.h2.jdbc.JdbcSQLNonTransientException: General error: "org.h2.mvstore.MVStoreException: The file is locked: /home/<USER>/Documents/expenses/expense-backend/data/expenses.mv.db [2.3.232/7]" [50000-232]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:566)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	... 62 more
Caused by: org.h2.mvstore.MVStoreException: The file is locked: /home/<USER>/Documents/expenses/expense-backend/data/expenses.mv.db [2.3.232/7]
	at org.h2.mvstore.DataUtils.newMVStoreException(DataUtils.java:996)
	at org.h2.mvstore.SingleFileStore.lockFileChannel(SingleFileStore.java:143)
	at org.h2.mvstore.SingleFileStore.open(SingleFileStore.java:117)
	at org.h2.mvstore.SingleFileStore.open(SingleFileStore.java:81)
	at org.h2.mvstore.MVStore.<init>(MVStore.java:286)
	... 56 more
2025-07-12 20:28:49.590049+01:00 database: flush
org.h2.message.DbException: General error: "org.h2.mvstore.MVStoreException: The file is locked: /home/<USER>/Documents/expenses/expense-backend/data/expenses.mv.db [2.3.232/7]" [50000-232]
	at org.h2.message.DbException.get(DbException.java:212)
	at org.h2.message.DbException.convert(DbException.java:407)
	at org.h2.mvstore.db.Store.lambda$new$0(Store.java:122)
	at org.h2.mvstore.MVStore.handleException(MVStore.java:1546)
	at org.h2.mvstore.MVStore.panic(MVStore.java:371)
	at org.h2.mvstore.MVStore.<init>(MVStore.java:291)
	at org.h2.mvstore.MVStore$Builder.open(MVStore.java:2035)
	at org.h2.mvstore.db.Store.<init>(Store.java:133)
	at org.h2.engine.Database.<init>(Database.java:326)
	at org.h2.engine.Engine.openSession(Engine.java:92)
	at org.h2.engine.Engine.openSession(Engine.java:222)
	at org.h2.engine.Engine.createSession(Engine.java:201)
	at org.h2.engine.SessionRemote.connectEmbeddedOrServer(SessionRemote.java:344)
	at org.h2.jdbc.JdbcConnection.<init>(JdbcConnection.java:124)
	at org.h2.Driver.connect(Driver.java:59)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:139)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:368)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:205)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:483)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:571)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:101)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:111)
	at org.hibernate.engine.jdbc.connections.internal.DatasourceConnectionProviderImpl.getConnection(DatasourceConnectionProviderImpl.java:126)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess.obtainConnection(JdbcEnvironmentInitiator.java:483)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcIsolationDelegate.delegateWork(JdbcIsolationDelegate.java:61)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.getJdbcEnvironmentUsingJdbcMetadata(JdbcEnvironmentInitiator.java:336)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:129)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator.initiateService(JdbcEnvironmentInitiator.java:81)
	at org.hibernate.boot.registry.internal.StandardServiceRegistryImpl.initiateService(StandardServiceRegistryImpl.java:130)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.createService(AbstractServiceRegistryImpl.java:263)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.initializeService(AbstractServiceRegistryImpl.java:238)
	at org.hibernate.service.internal.AbstractServiceRegistryImpl.getService(AbstractServiceRegistryImpl.java:215)
	at org.hibernate.boot.model.relational.Database.<init>(Database.java:45)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.getDatabase(InFlightMetadataCollectorImpl.java:226)
	at org.hibernate.boot.internal.InFlightMetadataCollectorImpl.<init>(InFlightMetadataCollectorImpl.java:194)
	at org.hibernate.boot.model.process.spi.MetadataBuildingProcess.complete(MetadataBuildingProcess.java:171)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.metadata(EntityManagerFactoryBuilderImpl.java:1442)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1513)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:66)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:390)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:419)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:400)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:366)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1873)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1822)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:207)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:970)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.example.expense_backend.ExpenseGuiApplication.init(ExpenseGuiApplication.java:19)
	at javafx.graphics@21.0.2/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:817)
	at javafx.graphics@21.0.2/com.sun.javafx.application.LauncherImpl.lambda$launchApplication$2(LauncherImpl.java:196)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.h2.jdbc.JdbcSQLNonTransientException: General error: "org.h2.mvstore.MVStoreException: The file is locked: /home/<USER>/Documents/expenses/expense-backend/data/expenses.mv.db [2.3.232/7]" [50000-232]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:566)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	... 62 more
Caused by: org.h2.mvstore.MVStoreException: The file is locked: /home/<USER>/Documents/expenses/expense-backend/data/expenses.mv.db [2.3.232/7]
	at org.h2.mvstore.DataUtils.newMVStoreException(DataUtils.java:996)
	at org.h2.mvstore.SingleFileStore.lockFileChannel(SingleFileStore.java:143)
	at org.h2.mvstore.SingleFileStore.open(SingleFileStore.java:117)
	at org.h2.mvstore.SingleFileStore.open(SingleFileStore.java:81)
	at org.h2.mvstore.MVStore.<init>(MVStore.java:286)
	... 56 more
2025-07-12 20:28:51.898952+01:00 database: flush
org.h2.message.DbException: General error: "org.h2.mvstore.MVStoreException: The file is locked: /home/<USER>/Documents/expenses/expense-backend/data/expenses.mv.db [2.3.232/7]" [50000-232]
	at org.h2.message.DbException.get(DbException.java:212)
	at org.h2.message.DbException.convert(DbException.java:407)
	at org.h2.mvstore.db.Store.lambda$new$0(Store.java:122)
	at org.h2.mvstore.MVStore.handleException(MVStore.java:1546)
	at org.h2.mvstore.MVStore.panic(MVStore.java:371)
	at org.h2.mvstore.MVStore.<init>(MVStore.java:291)
	at org.h2.mvstore.MVStore$Builder.open(MVStore.java:2035)
	at org.h2.mvstore.db.Store.<init>(Store.java:133)
	at org.h2.engine.Database.<init>(Database.java:326)
	at org.h2.engine.Engine.openSession(Engine.java:92)
	at org.h2.engine.Engine.openSession(Engine.java:222)
	at org.h2.engine.Engine.createSession(Engine.java:201)
	at org.h2.engine.SessionRemote.connectEmbeddedOrServer(SessionRemote.java:344)
	at org.h2.jdbc.JdbcConnection.<init>(JdbcConnection.java:124)
	at org.h2.Driver.connect(Driver.java:59)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:139)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:368)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:205)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:483)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:571)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:101)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:111)
	at org.hibernate.engine.jdbc.connections.internal.DatasourceConnectionProviderImpl.getConnection(DatasourceConnectionProviderImpl.java:126)
	at org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess.obtainConnection(JdbcEnvironmentInitiator.java:483)
	at org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection(DdlTransactionIsolatorNonJtaImpl.java:46)
	at org.hibernate.resource.transaction.backend.jdbc.internal.DdlTransactionIsolatorNonJtaImpl.getIsolatedConnection(DdlTransactionIsolatorNonJtaImpl.java:39)
	at org.hibernate.tool.schema.internal.exec.ImprovedExtractionContextImpl.getJdbcConnection(ImprovedExtractionContextImpl.java:63)
	at org.hibernate.tool.schema.extract.spi.ExtractionContext.getQueryResults(ExtractionContext.java:43)
	at org.hibernate.tool.schema.extract.internal.SequenceInformationExtractorLegacyImpl.extractMetadata(SequenceInformationExtractorLegacyImpl.java:39)
	at org.hibernate.tool.schema.extract.internal.DatabaseInformationImpl.initializeSequences(DatabaseInformationImpl.java:66)
	at org.hibernate.tool.schema.extract.internal.DatabaseInformationImpl.<init>(DatabaseInformationImpl.java:60)
	at org.hibernate.tool.schema.internal.Helper.buildDatabaseInformation(Helper.java:185)
	at org.hibernate.tool.schema.internal.AbstractSchemaMigrator.doMigration(AbstractSchemaMigrator.java:93)
	at org.hibernate.tool.schema.spi.SchemaManagementToolCoordinator.performDatabaseAction(SchemaManagementToolCoordinator.java:280)
	at org.hibernate.tool.schema.spi.SchemaManagementToolCoordinator.lambda$process$5(SchemaManagementToolCoordinator.java:144)
	at java.base/java.util.HashMap.forEach(HashMap.java:1429)
	at org.hibernate.tool.schema.spi.SchemaManagementToolCoordinator.process(SchemaManagementToolCoordinator.java:141)
	at org.hibernate.boot.internal.SessionFactoryObserverForSchemaExport.sessionFactoryCreated(SessionFactoryObserverForSchemaExport.java:37)
	at org.hibernate.internal.SessionFactoryObserverChain.sessionFactoryCreated(SessionFactoryObserverChain.java:35)
	at org.hibernate.internal.SessionFactoryImpl.<init>(SessionFactoryImpl.java:324)
	at org.hibernate.boot.internal.SessionFactoryBuilderImpl.build(SessionFactoryBuilderImpl.java:463)
	at org.hibernate.jpa.boot.internal.EntityManagerFactoryBuilderImpl.build(EntityManagerFactoryBuilderImpl.java:1517)
	at org.springframework.orm.jpa.vendor.SpringHibernateJpaPersistenceProvider.createContainerEntityManagerFactory(SpringHibernateJpaPersistenceProvider.java:66)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.createNativeEntityManagerFactory(LocalContainerEntityManagerFactoryBean.java:390)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.buildNativeEntityManagerFactory(AbstractEntityManagerFactoryBean.java:419)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.afterPropertiesSet(AbstractEntityManagerFactoryBean.java:400)
	at org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean.afterPropertiesSet(LocalContainerEntityManagerFactoryBean.java:366)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1873)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1822)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:207)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:970)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at com.example.expense_backend.ExpenseGuiApplication.init(ExpenseGuiApplication.java:19)
	at javafx.graphics@21.0.2/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:817)
	at javafx.graphics@21.0.2/com.sun.javafx.application.LauncherImpl.lambda$launchApplication$2(LauncherImpl.java:196)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.h2.jdbc.JdbcSQLNonTransientException: General error: "org.h2.mvstore.MVStoreException: The file is locked: /home/<USER>/Documents/expenses/expense-backend/data/expenses.mv.db [2.3.232/7]" [50000-232]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:566)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	... 66 more
Caused by: org.h2.mvstore.MVStoreException: The file is locked: /home/<USER>/Documents/expenses/expense-backend/data/expenses.mv.db [2.3.232/7]
	at org.h2.mvstore.DataUtils.newMVStoreException(DataUtils.java:996)
	at org.h2.mvstore.SingleFileStore.lockFileChannel(SingleFileStore.java:143)
	at org.h2.mvstore.SingleFileStore.open(SingleFileStore.java:117)
	at org.h2.mvstore.SingleFileStore.open(SingleFileStore.java:81)
	at org.h2.mvstore.MVStore.<init>(MVStore.java:286)
	... 60 more
