#!/bin/bash

# Expense Tracker Desktop Launcher Installation Script
# This script installs the desktop launcher for the Expense Tracker application

echo "Installing Expense Tracker Desktop Launcher..."

# Get the current directory (where the application is located)
APP_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
echo "Application directory: $APP_DIR"

# Create directories if they don't exist
mkdir -p ~/.local/share/applications
mkdir -p ~/.local/share/icons

# Copy the desktop file to the applications directory
DESKTOP_FILE="$HOME/.local/share/applications/expense-tracker.desktop"
cat > "$DESKTOP_FILE" << EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=Expense Tracker
Comment=Personal expense tracking application with JavaFX GUI
Exec=$APP_DIR/launch-expense-tracker.sh
Icon=$APP_DIR/expense-tracker-icon.svg
Path=$APP_DIR
Terminal=false
StartupNotify=true
Categories=Office;Finance;
Keywords=expense;money;finance;budget;tracker;
MimeType=application/x-expense-tracker;
StartupWMClass=com-example-expense_backend-ExpenseGuiApplication
EOF

# Make the desktop file executable
chmod +x "$DESKTOP_FILE"

# Make sure the launch script is executable
chmod +x "$APP_DIR/launch-expense-tracker.sh"

# Update desktop database (if available)
if command -v update-desktop-database &> /dev/null; then
    update-desktop-database ~/.local/share/applications
    echo "Desktop database updated"
fi

# Copy desktop file to Desktop if it exists and user wants it
if [ -d "$HOME/Desktop" ]; then
    read -p "Do you want to create a desktop shortcut? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        cp "$DESKTOP_FILE" "$HOME/Desktop/"
        chmod +x "$HOME/Desktop/expense-tracker.desktop"
        echo "Desktop shortcut created"
    fi
fi

echo ""
echo "Installation completed successfully!"
echo ""
echo "You can now:"
echo "1. Find 'Expense Tracker' in your application menu"
echo "2. Search for 'expense' in your application launcher"
if [ -f "$HOME/Desktop/expense-tracker.desktop" ]; then
    echo "3. Double-click the desktop shortcut"
fi
echo ""
echo "To uninstall, run: rm ~/.local/share/applications/expense-tracker.desktop"
if [ -f "$HOME/Desktop/expense-tracker.desktop" ]; then
    echo "                   rm ~/Desktop/expense-tracker.desktop"
fi
echo ""
echo "Application location: $APP_DIR"
