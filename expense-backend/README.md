# Expense Tracker

A native Linux desktop application for tracking personal expenses, built with JavaFX and Spring Boot.

## Features

- **Native Linux GUI** - Built with JavaFX for a responsive desktop experience
- **Expense Management** - Add, edit, delete, and view expenses
- **Categories** - Organize expenses by customizable categories
- **Search & Filter** - Find expenses by description, category, or date range
- **Summary Statistics** - View totals, counts, and averages
- **Data Persistence** - Uses H2 database for reliable data storage
- **Desktop Integration** - Launches from application menu or desktop shortcut

## Requirements

- Java 21 or later
- Linux desktop environment
- Maven (included via wrapper)

## Installation

1. **Clone or download** this project to your desired location
2. **Run the installer** to set up desktop integration:
   ```bash
   cd expense-backend
   ./install-desktop-launcher.sh
   ```

## Running the Application

### Option 1: Desktop Launcher (Recommended)
- Find "Expense Tracker" in your application menu
- Search for "expense" in your application launcher
- Double-click the desktop shortcut (if created)

### Option 2: Command Line
```bash
cd expense-backend
./launch-expense-tracker.sh
```

### Option 3: <PERSON><PERSON> (Development)
```bash
cd expense-backend
./mvnw javafx:run
```

## Usage

### Adding Expenses
1. Fill in the form on the right side:
   - **Description**: What the expense was for
   - **Amount**: Cost in decimal format (e.g., 25.99)
   - **Date**: When the expense occurred
   - **Category**: Select existing or type new category
   - **Notes**: Optional additional details
2. Click "Add" to save the expense

### Managing Expenses
- **Edit**: Select an expense from the table, modify the form, and click "Update"
- **Delete**: Select an expense and click "Delete" (confirmation required)
- **Clear**: Reset the form fields

### Searching and Filtering
- **Search by description**: Type in the search field
- **Filter by category**: Select from the category dropdown
- **Filter by date range**: Use the From/To date pickers
- **Clear filters**: Click "Clear Filters" to show all expenses

### Summary Information
The bottom of the expense list shows:
- **Total**: Sum of all displayed expenses
- **Count**: Number of expenses shown
- **Average**: Average expense amount

## Data Storage

- Expenses are stored in an H2 database file: `./data/expenses.mv.db`
- Data persists between application sessions
- Automatic database schema creation and updates

## Troubleshooting

### Application Won't Start
1. Check Java version: `java -version` (must be 21+)
2. Ensure script is executable: `chmod +x launch-expense-tracker.sh`
3. Run from terminal to see error messages: `./launch-expense-tracker.sh`

### Desktop Launcher Issues
1. Re-run the installer: `./install-desktop-launcher.sh`
2. Log out and back in to refresh the desktop environment
3. Check if the desktop file exists: `ls ~/.local/share/applications/expense-tracker.desktop`

### Database Issues
- Database files are created in `./data/` directory
- To reset all data, delete the `./data/` folder
- Check file permissions if database errors occur

## Development

### Building
```bash
./mvnw clean compile
```

### Running Tests
```bash
./mvnw test
```

### Project Structure
```
expense-backend/
├── src/main/java/com/example/expense_backend/
│   ├── entity/          # JPA entities (Expense)
│   ├── repository/      # Data access layer
│   ├── service/         # Business logic
│   ├── gui/             # JavaFX controllers
│   └── ExpenseGuiApplication.java
├── src/main/resources/
│   ├── fxml/            # JavaFX layouts
│   └── application.properties
├── launch-expense-tracker.sh
├── install-desktop-launcher.sh
└── expense-tracker-icon.svg
```

## Uninstalling

To remove the desktop integration:
```bash
rm ~/.local/share/applications/expense-tracker.desktop
rm ~/Desktop/expense-tracker.desktop  # if desktop shortcut was created
```

To completely remove the application:
1. Remove desktop integration (above)
2. Delete the application folder
3. Optionally remove data: `rm -rf /path/to/expense-backend/data/`

## License

This project is open source. Feel free to modify and distribute as needed.

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Run the application from terminal to see detailed error messages
3. Ensure all requirements are met (Java 21+, proper permissions)
